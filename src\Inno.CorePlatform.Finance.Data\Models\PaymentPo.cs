﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    [Table("Payment")]
    [Comment("付款单表")]
    public class PaymentPo : BasePo
    {
        /// <summary>
        /// 付款单号
        /// </summary>
        [Comment("付款单号")]
        [MaxLength(200)]
        public string? Code { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        [Comment("单据日期")]
        public DateTime BillDate { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        [Comment("采购单号")]
        public string? PurchaseCode { get; set; }
        /// <summary>
        /// 厂家单号
        /// </summary>
        [Comment("厂家单号")]

        public string? ProducerOrderNo { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string? NameCode { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>

        public Guid? ServiceId { get; set; }
        public string? ServiceName { get; set; }
        /// <summary>
        /// 供应商Id
        /// </summary>  
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>  
        public string? AgentName { get; set; }


        /// <summary>
        /// 付款单类型
        /// </summary>
        [Comment("付款单类型")]
        public PaymentTypeEnum Type { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        [Comment("付款金额")]
        [Precision(18, 2)]
        public decimal Value { get; set; }
        /// <summary>
        /// 近期订单使用金额
        /// </summary>
        [Comment("近期订单使用金额")]
        [Precision(18, 2)]
        public decimal? NearOrderUseValue { get; set; }

        /// <summary>
        /// 付款时间
        /// </summary>
        [Comment("付款时间")]
        public DateTime? PaymentDate { get; set; }

        /// <summary>
        /// 冲销状态
        /// </summary> 
        [Comment("冲销状态")]
        public AbatedStatusEnum? AbatedStatus { get; set; }

        /// <summary>
        /// 支付类型（现金支付，承兑发票）
        /// </summary>
        [Comment("支付类型")]
        [MaxLength(200)]
        public string? PayClassify { get; set; }

        /// <summary>
        /// 采购申请Id
        /// </summary>
        public Guid? RelateId { get; set; }

        public AdvancePayModeEnum? AdvancePayMode { get; set; }

        /// <summary>
        /// 额度金额
        /// </summary>
        [Comment("额度金额")]
        [Precision(18, 2)]
        public decimal? CreditAmount { get; set; }

        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 币种代码 
        /// </summary>

        [MaxLength(200)]
        public string? CoinCode { get; set; }
        /// <summary>
        /// 币种名称
        /// </summary>

        [MaxLength(200)]
        public string? CoinName { get; set; }

        /// <summary>
        /// 人民币金额
        /// </summary>
        [Precision(18, 2)]
        public decimal? RMBAmount { get; set; }
        /// <summary>
        /// 项目单号 
        /// </summary> 

        [Comment("项目单号")]
        [MaxLength(200)]
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称 
        /// </summary> 

        [Comment("项目名称")]
        [MaxLength(200)]
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary> 

        [Comment("项目Id")]
        public Guid? ProjectId { get; set; }

        [Comment("批量付款单号")]
        [MaxLength(200)]
        public string? PaymentAutoItemCode { get; set; }

        /// <summary>
        /// 采购合同单号
        /// </summary>
        [MaxLength(500)]
        [Comment("采购合同单号")]
        public string? PurchaseContactNo { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary> 
        public string? CustomerName { get; set; }

        /// <summary>
        /// 原始付款单号
        /// </summary>
        [Comment("元素付款单号")]
        [MaxLength(200)]
        public string? OriginCode { get; set; }

        /// <summary>
        /// 限定折扣金额
        /// </summary>
        [Comment("限定折扣金额")]
        [Precision(18, 2)]
        public decimal? LimitedDiscount { get; set; }

        /// <summary>
        /// 附件Ids
        /// </summary>
        [Comment("附件Ids")]
        public string? AttachFileIds { get; set; }

        /// <summary>
        /// OA请求Id
        /// </summary>
        [Comment("OA请求Id")]
        [MaxLength(500)]
        public string? OARequestId { get; set; }
    }
}
