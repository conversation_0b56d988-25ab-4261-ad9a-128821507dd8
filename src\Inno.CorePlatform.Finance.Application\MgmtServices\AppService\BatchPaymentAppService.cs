﻿using Dapr.Client;
using Inno.CorePlatform.Common.Clients.Interfaces;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.DTO.CodeGeneration;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.EPPlus;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.DebtDetail;
using Inno.CorePlatform.Finance.Application.DTOs.OM;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.DTOs.Projects;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.PaymentAggregate;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.Gateway.Client;
using Inno.CorePlatform.Gateway.Client.WeaverOA;
using Inno.CorePlatform.Gateway.Common.WeaverOA;
using Inno.CorePlatform.ServiceClient;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using OfficeOpenXml;
using System.Drawing;
using System.Linq;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class BatchPaymentAppService : IBatchPaymentAppService
    {
        private readonly FinanceDbContext _db;
        private readonly IConfiguration _configuration;
        private readonly IWeaverApiClient _weaverApiClient;
        private readonly ILogger<BatchPaymentAppService> _logger;
        private readonly IPaymentAutoItemRepository _paymentAutoItemRepository;
        private readonly ICodeGenClient _codeGenClient;
        private readonly PortInterfaces.Clients.IBDSApiClient _bDSApiClient;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IDebtDetailRepository _debtDetailRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBaseAllQueryService<PaymentAutoAgentBankInfoPo> _paymentAutoAgentBankInfoQueryService;
        private readonly IBaseAllQueryService<PaymentAutoDetailPo> _autoDetailQueryService;
        private readonly IBaseAllQueryService<DebtDetailPo> _debtDetailQueryService;
        private readonly IBaseAllQueryService<DebtPo> _debtQueryService;
        private readonly IPaymentAutoDetailRepository _paymentAutoDetailRepository;
        private readonly IBulkPaymentQueryService _bulkPaymentQueryService;
        private readonly IPaymentAutoAgent _paymentAutoAgent;
        private readonly IAppServiceContextAccessor _appServiceContextAccessor;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly IProjectApiExcuteClient _projectApiExcuteClient;
        private readonly PortInterfaces.Clients.IPurchaseApiClient _purchaseApiClient;
        private readonly IOMApiClient _oMApiClient;
        private readonly IEquipmentsApiClient _equipmentsApiClient;
        private readonly PortInterfaces.Clients.ISellApiClient _sellApiClient;
        private readonly IFileGatewayClient _fileGatewayClient;
        private readonly ICoordinateClient _coordinateclient;
        private readonly IUCApiClient _uCApiClient;
        private readonly IInventoryApiClient _iInventoryApiClient;
        private readonly DaprClient _daprClient;
        public BatchPaymentAppService(IPaymentAutoItemRepository paymentAutoItemRepository,
                                         ICodeGenClient codeGenClient,
                                         PortInterfaces.Clients.IBDSApiClient bDSApiClient,
                                         IKingdeeApiClient kingdeeApiClient,
                                         IDebtDetailRepository debtDetailRepository,
                                         IUnitOfWork unitOfWork,
                                         IBaseAllQueryService<PaymentAutoDetailPo> autoDetailQueryService,
                                         IPaymentAutoDetailRepository paymentAutoDetailRepository,
                                         IConfiguration configuration,
                                         IWeaverApiClient weaverApiClient,
                                         ILogger<BatchPaymentAppService> logger,
                                         IPaymentAutoAgent paymentAutoAgent,
                                         IAppServiceContextAccessor appServiceContextAccessor,
                                         IBulkPaymentQueryService bulkPaymentQueryService,
                                         IBaseAllQueryService<DebtDetailPo> debtDetailQueryService,
                                         IBaseAllQueryService<DebtPo> debtQueryService,
                                         IProjectMgntApiClient projectMgntApiClient,
                                         PortInterfaces.Clients.IPurchaseApiClient purchaseApiClient,
                                         IProjectApiExcuteClient projectApiExcuteClient,
                                         PortInterfaces.Clients.ISellApiClient sellApiClient,
                                         IEquipmentsApiClient equipmentsApiClient,
                                         FinanceDbContext db,
                                         IBaseAllQueryService<PaymentAutoAgentBankInfoPo> paymentAutoAgentBankInfoQueryService,
                                         IOMApiClient oMApiClient,
                                         IFileGatewayClient fileGatewayClient,
                                         ICoordinateClient coordinateclient,
                                         IInventoryApiClient iInventoryApiClient,
                                         DaprClient daprClient,
        IUCApiClient uCApiClient)
        {
            _configuration = configuration;
            _weaverApiClient = weaverApiClient;
            _logger = logger;
            _paymentAutoItemRepository = paymentAutoItemRepository;
            _codeGenClient = codeGenClient;
            _bDSApiClient = bDSApiClient;
            _kingdeeApiClient = kingdeeApiClient;
            _debtDetailRepository = debtDetailRepository;
            _unitOfWork = unitOfWork;
            _autoDetailQueryService = autoDetailQueryService;
            _paymentAutoDetailRepository = paymentAutoDetailRepository;
            _bulkPaymentQueryService = bulkPaymentQueryService;
            _paymentAutoAgent = paymentAutoAgent;
            _appServiceContextAccessor = appServiceContextAccessor;
            _debtDetailQueryService = debtDetailQueryService;
            _debtQueryService = debtQueryService;
            _projectMgntApiClient = projectMgntApiClient;
            _projectApiExcuteClient = projectApiExcuteClient;
            _purchaseApiClient = purchaseApiClient;
            _oMApiClient = oMApiClient;
            _db = db;
            _sellApiClient = sellApiClient;
            _equipmentsApiClient = equipmentsApiClient;
            _paymentAutoAgentBankInfoQueryService = paymentAutoAgentBankInfoQueryService;
            _fileGatewayClient = fileGatewayClient;
            _coordinateclient = coordinateclient;
            _uCApiClient = uCApiClient;
            _iInventoryApiClient = iInventoryApiClient;
            _daprClient = daprClient;
        }

        public async Task<int> CreateAsync(PaymentAutoItemInput input)
        {
            try
            {
                var debtDetilIds = input.DetailInput.Select(x => x.DebtDetilId).Distinct().ToList();
                var paymentAutoDetails = await _db.PaymentAutoDetails.Include(x => x.PaymentAutoItem).Where(x => debtDetilIds.Any(p => p == x.DebtDetilId)).AsNoTracking().ToListAsync();
                if (paymentAutoDetails != null && paymentAutoDetails.Any())
                {
                    var codes = paymentAutoDetails.Select(x => x.PaymentAutoItem.Code).Distinct().ToList();
                    var codeStr = string.Join(",", codes);
                    throw new AppServiceException($"选择的付款计划在“{codeStr}”中存在，请刷新后操作！");
                }
                input.BusinessArea = "FXBD";
                var dept = await _bDSApiClient.GetBusinessDeptById(input.BusinessDeptFullPath, "none", Guid.NewGuid().ToString());
                if (dept != null && dept.Count > 0)
                {
                    input.BusinessArea = dept[0].Value;
                }
                var paymentAutoItem = input.Adapt<PaymentAutoItem>();
                paymentAutoItem.CompanyId = input.CompanyId;
                paymentAutoItem.CompanyName = input.DetailInput.FirstOrDefault().companyName;
                paymentAutoItem.BusinessDeptFullName = input.BusinessDeptFullName;
                paymentAutoItem.BusinessDeptFullPath = input.BusinessDeptFullPath;
                paymentAutoItem.BusinessDeptId = input.BusinessDeptId;

                paymentAutoItem.Id = Guid.NewGuid();
                paymentAutoItem.Status = PaymentAutoItemStatusEnum.WaitSubmit;
                foreach (var item in input.DetailInput)
                {
                    item.PaymentAutoItemId = paymentAutoItem.Id;
                    paymentAutoItem.AddPaymentAutoDetail(item.Adapt<PaymentAutoDetail>(), input.CreatedBy);
                }

                //生成单号
                var companyInfoOutput = await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput { ids = new List<string> { paymentAutoItem.CompanyId.ToString() } });
                var companyInfo = companyInfoOutput?.FirstOrDefault();
                if (companyInfo == null)
                {
                    throw new AppServiceException("公司信息不存在");
                }

                paymentAutoItem.NameCode = companyInfo.nameCode;
                var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                {
                    BusinessArea = input.BusinessArea,
                    BillType = "AUPA",
                    SysMonth = companyInfo.sysMonth,
                    DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                    Num = 1,
                    CompanyCode = companyInfo.nameCode
                });
                if (outPut.Status)
                {
                    paymentAutoItem.Code = outPut.Codes.First();
                    var sysMonth = await _bDSApiClient.GetSystemMonth(companyInfo.companyId);
                    DateTime.TryParse(sysMonth, out DateTime billDate);
                    paymentAutoItem.BillDate = billDate;
                }
                else
                {
                    throw new ApplicationException($"生成Code失败，{outPut.Msg}");
                }
                if (string.IsNullOrEmpty(outPut.Codes[0]))
                {
                    throw new AppServiceException("单号生成异常，请重试！");
                }

                paymentAutoItem.CreateBy(input.CreatedBy);


                await _paymentAutoItemRepository.AddAsync(paymentAutoItem);
                var res = await _unitOfWork.CommitAsync();

                var dentailIds = input.DetailInput.Select(x => x.DebtDetilId).Distinct().ToList();
                var paymentDetails = await (from debtdetail in _db.DebtDetails
                                            join debt in _db.Debts on debtdetail.DebtId equals debt.Id into tempdebt
                                            from e in tempdebt.DefaultIfEmpty()
                                            where debtDetilIds.Contains(debtdetail.Id)
                                            select new PaymentDetailOutput
                                            {
                                                AgentId = e.AgentId,
                                                AgentName = e.AgentName,
                                            }).AsNoTracking().ToListAsync();

                var agentIds = paymentDetails.Select(x => x.AgentId.Value).Distinct().ToList();
                var agents = await _bDSApiClient.GetAgentBankInfoByAgentIds(agentIds);
                List<PaymentAutoAgentBankInfo> autoAgentBankInfoPos = new List<PaymentAutoAgentBankInfo>();
                foreach (var agent in agents)
                {
                    var agentBankInfo = agent.Adapt<PaymentAutoAgentBankInfo>();
                    var bankReceipt = GetAgentBankRecipt(paymentAutoItem.CompanyId.Value, agent.receiptList);
                    agentBankInfo.PaymentAutoItemId = paymentAutoItem.Id;
                    agentBankInfo.AgentId = agent.agentId;
                    agentBankInfo.AgentName = agent.agentName;
                    agentBankInfo.BankName = bankReceipt?.bank;
                    agentBankInfo.BankCode = bankReceipt?.bankCode;
                    agentBankInfo.Account = bankReceipt?.bankNo;
                    agentBankInfo.AccountName = bankReceipt?.account;
                    agentBankInfo.PayClassify = "JSFS04";
                    agentBankInfo.CreateBy(input.CreatedBy);
                    autoAgentBankInfoPos.Add(agentBankInfo);
                }

                if (autoAgentBankInfoPos != null && autoAgentBankInfoPos.Count > 0)
                {
                    await _paymentAutoAgent.CreateBankInfos(autoAgentBankInfoPos);
                }

                return res;
            }
            catch (Exception ex)
            {

                throw new ApplicationException(ex.Message);
            }
        }
        public async Task<int> UpdateAsync(PaymentAutoItemInput input)
        {
            var paymentAutoItem = input.Adapt<PaymentAutoItem>();
            paymentAutoItem.UpdateBy(input.UpdatedBy);
            await _paymentAutoItemRepository.UpdateAsync(paymentAutoItem);
            var res = await _unitOfWork.CommitAsync();
            return res;
        }

        public async Task<int> DeleteAsync(Guid id)
        {
            var item = await _paymentAutoItemRepository.GetAsync(id);
            if (item == null)
            {
                throw new AppServiceException("数据不存在，请刷新页面!");
            }
            if (item.Status != Domain.PaymentAutoItemStatusEnum.WaitSubmit)
            {
                throw new AppServiceException("只有待提交状态的数据才能删除");
            }
            if (item.CreatedBy != _appServiceContextAccessor.Get().UserName)
            {
                throw new AppServiceException("非创建人不能删除");
            }

            var lstDetailId = (await _autoDetailQueryService.GetAllListAsync(t => t.PaymentAutoItemId == item.Id, null, q => new PaymentAutoDetailPo { Id = q.Id })).Select(t => t.Id).ToList();

            var res = await _paymentAutoDetailRepository.DeteteManyAsync(lstDetailId);
            await _paymentAutoAgent.DeleteByPaymentAutoItemId(id);
            await _paymentAutoItemRepository.DeleteAsync(id);
            if (!string.IsNullOrEmpty(item.OARequestId))
            {
                //删除OA单据
                await _weaverApiClient.DelWorkFlow(item.CreatedBy, Convert.ToInt32(item.OARequestId));
            }
            res = await _unitOfWork.CommitAsync();
            return res;
        }

        public async Task<int> AddDetailAsync(List<PaymentAutoDetailInput> lstDetail, string userName)
        {
            if (lstDetail.Count <= 0)
            {
                throw new AppServiceException("请添加明细后再执行操作！");
            }

            var isNotSameItem = lstDetail.GroupBy(x => x.PaymentAutoItemId).Count() > 1;

            if (isNotSameItem)
            {
                throw new AppServiceException("要添加的明细中含有多个批量付款单的明细,只能选择同一条批量付款单进行操作！");
            }
            var paymentAutoItemId = lstDetail[0].PaymentAutoItemId;
            var item = await _paymentAutoItemRepository.GetWithNoTrackAsync(paymentAutoItemId.Value);
            if (item == null || item.Status != PaymentAutoItemStatusEnum.WaitSubmit)
            {
                throw new AppServiceException("批量付款单状态不是待提交状态，不能添加明细！");
            }

            var debtDetilIds = lstDetail.Select(x => x.DebtDetilId).Distinct().ToList();
            var paymentAutoDetails = await _db.PaymentAutoDetails.Include(x => x.PaymentAutoItem).Where(x => x.PaymentAutoItemId != paymentAutoItemId && debtDetilIds.Any(p => p == x.DebtDetilId)).AsNoTracking().ToListAsync();
            if (paymentAutoDetails != null && paymentAutoDetails.Any())
            {
                var codes = paymentAutoDetails.Select(x => x.PaymentAutoItem.Code).Distinct().ToList();
                var codeStr = string.Join(",", codes);
                throw new AppServiceException($"选择的付款计划在“{codeStr}”中存在，请刷新后操作！");
            }

            var lstDetailPo = await _autoDetailQueryService.GetAllListAsync(t => t.PaymentAutoItemId == item.Id);

            foreach (var detail in lstDetail)
            {
                if (lstDetailPo.Exists(t => t.DebtDetilId == detail.DebtDetilId) == false)
                {
                    detail.Id = Guid.Empty;
                    item.AddPaymentAutoDetail(detail.Adapt<PaymentAutoDetail>(), userName);
                }
            }
            await _paymentAutoItemRepository.UpdateAsync(item);

            var paymentDetails = await (from debtdetail in _db.DebtDetails
                                        join debt in _db.Debts on debtdetail.DebtId equals debt.Id into tempdebt
                                        from e in tempdebt.DefaultIfEmpty()
                                        where debtDetilIds.Contains(debtdetail.Id)
                                        select new PaymentDetailOutput
                                        {
                                            AgentId = e.AgentId,
                                            AgentName = e.AgentName,
                                        }).AsNoTracking().ToListAsync();

            var agentIds = paymentDetails.Select(x => x.AgentId.Value).Distinct().ToList();
            var agentBankInfoPos = await _paymentAutoAgentBankInfoQueryService.GetAllListAsync(t => t.PaymentAutoItemId == item.Id);
            if (agentBankInfoPos != null && agentBankInfoPos.Count > 0)
            {
                var agentIdSet = new HashSet<Guid>(agentBankInfoPos.Select(t => t.AgentId.Value));
                agentIds.RemoveAll(agentId => agentIdSet.Contains(agentId));
            }

            if (agentIds.Count > 0)
            {
                var agents = await _bDSApiClient.GetAgentBankInfoByAgentIds(agentIds);
                List<PaymentAutoAgentBankInfoPo> autoAgentBankInfoPos = new List<PaymentAutoAgentBankInfoPo>();
                foreach (var agent in agents)
                {
                    var agentBankInfo = agent.Adapt<PaymentAutoAgentBankInfoPo>();
                    receipt? bankReceipt = GetAgentBankRecipt(item.CompanyId.Value, agent.receiptList);
                    agentBankInfo.PaymentAutoItemId = item.Id;
                    agentBankInfo.AgentId = agent.agentId;
                    agentBankInfo.AgentName = agent.agentName;
                    agentBankInfo.BankName = bankReceipt?.bank;
                    agentBankInfo.BankCode = bankReceipt?.bankCode;
                    agentBankInfo.Account = bankReceipt?.bankNo;
                    agentBankInfo.AccountName = bankReceipt?.account;
                    agentBankInfo.PayClassify = "JSFS04";
                    agentBankInfo.CreatedBy = userName;
                    agentBankInfo.CreatedTime = DateTime.UtcNow;
                    autoAgentBankInfoPos.Add(agentBankInfo);
                }

                if (autoAgentBankInfoPos != null && autoAgentBankInfoPos.Count > 0)
                {
                    await _db.PaymentAutoAgentBankInfos.AddRangeAsync(autoAgentBankInfoPos);
                }
            }

            var res = await _unitOfWork.CommitAsync();

            return res;
        }

        private static receipt? GetAgentBankRecipt(Guid companyId, List<receipt> receipts)
        {
            var bankReceipt = receipts.FirstOrDefault(i => i.companyList != null && i.companyList.Contains(companyId));
            if (bankReceipt == null)
            {
                bankReceipt = receipts.FirstOrDefault();
            }
            return bankReceipt;
        }

        public async Task<int> DeleteDetailAsync(PaymentAutoDetailDeleteInput input)
        {
            if (input.ids.Count <= 0)
            {
                throw new AppServiceException("请至少选择一条要删除的明细后再执行操作！");
            }
            var item = await _paymentAutoItemRepository.GetAsync(input.PaymentAutoItemId);
            if (item == null)
            {
                throw new AppServiceException("数据不存在，请刷新页面!");
            }
            if (item.Status != Domain.PaymentAutoItemStatusEnum.WaitSubmit)
            {
                throw new AppServiceException("只有待提交状态的批量付款单才能删除明细");
            }

            var res = await _paymentAutoDetailRepository.DeteteManyAsync(input.ids, true);

            return res;
        }

        public async Task<int> ReAddDetailAsync(List<PaymentAutoDetailInput> lstDetail, string userName)
        {
            if (lstDetail.Count <= 0)
            {
                throw new AppServiceException("请添加明细后再执行操作！");
            }
            var isNotSameItem = lstDetail.GroupBy(x => x.PaymentAutoItemId).Count() > 1;

            if (isNotSameItem)
            {
                throw new AppServiceException("要添加的明细中含有多个批量付款单的明细,只能选择同一条批量付款单进行操作！");
            }

            var paymentAutoItemId = lstDetail[0].PaymentAutoItemId;
            var item = await _paymentAutoItemRepository.GetWithNoTrackAsync(paymentAutoItemId.Value);
            if (item == null || item.Status != PaymentAutoItemStatusEnum.WaitSubmit)
            {
                throw new AppServiceException("批量付款单状态不是待提交状态，不能添加明细！");
            }

            var debtDetilIds = lstDetail.Select(x => x.DebtDetilId).Distinct().ToList();
            var paymentAutoDetails = await _db.PaymentAutoDetails.Include(x => x.PaymentAutoItem).Where(x => x.PaymentAutoItemId != paymentAutoItemId && debtDetilIds.Any(p => p == x.DebtDetilId)).AsNoTracking().ToListAsync();
            if (paymentAutoDetails != null && paymentAutoDetails.Any())
            {
                var codes = paymentAutoDetails.Select(x => x.PaymentAutoItem.Code).Distinct().ToList();
                var codeStr = string.Join(",", codes);
                throw new AppServiceException($"选择的付款计划在“{codeStr}”中存在，请刷新后操作！");
            }

            var lstDetailId = (await _autoDetailQueryService.GetAllListAsync(t => t.PaymentAutoItemId == paymentAutoItemId, null, q => new PaymentAutoDetailPo { Id = q.Id })).Select(t => t.Id).ToList();


            foreach (var detail in lstDetail)
            {
                detail.Id = Guid.Empty;
                item.AddPaymentAutoDetail(detail.Adapt<PaymentAutoDetail>(), userName);
            }
            await _paymentAutoItemRepository.UpdateAsync(item);

            var res = await _unitOfWork.CommitAsync();

            await _paymentAutoDetailRepository.DeteteManyAsync(lstDetailId, true);
            return res;
        }

        public async Task<int> SubmitOrCancelAsync(Guid id, string userName, int type = 0)
        {
            var item = await _paymentAutoItemRepository.GetWithNoTrackAsync(id);
            if (item == null)
            {
                throw new AppServiceException("数据不存在，请刷新页面!");
            }
            if (type == 0) //提交
            {
                if (item.Status != Domain.PaymentAutoItemStatusEnum.WaitSubmit)
                {
                    throw new AppServiceException("只有待提交状态才能提交");
                }
                item.Status = Domain.PaymentAutoItemStatusEnum.Auditing;
            }
            else  //撤回
            {
                if (item.Status != Domain.PaymentAutoItemStatusEnum.Auditing)
                {
                    throw new AppServiceException("只有审批中状态才能取消");
                }
                item.Status = Domain.PaymentAutoItemStatusEnum.WaitSubmit;
            }
            item.UpdateBy(userName);

            await _paymentAutoItemRepository.UpdateAsync(item);

            var res = await _unitOfWork.CommitAsync();

            return res;

        }
        #region 审批
        /// <summary>
        /// 提交审核
        /// </summary>
        /// <param name="id"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public async Task<bool> SubmitAsync(Guid id, string userName)
        {
            try
            {
                var paymentAuto = await _paymentAutoItemRepository.GetWithNoTrackAsync(id);
                if (paymentAuto == null || paymentAuto.Status != PaymentAutoItemStatusEnum.WaitSubmit)
                {
                    throw new ApplicationException("操作失败，原因：当前批量付款不存在");
                }
                if (paymentAuto.PaymentAutoDetails == null || paymentAuto.PaymentAutoDetails.Count <= 0)
                {
                    throw new ApplicationException("操作失败，原因：当前批量付款不存在【付款信息】，请修正数据再提交");
                }
                if (paymentAuto.PaymentAutoDetails.Sum(t => t.Value) <= 0)
                {
                    throw new ApplicationException("操作失败，原因：付款金额不能小于0，请修正数据再提交");
                }

                //批量付款检查采购订单的合同是否回签
                var debtDetilIds = paymentAuto.PaymentAutoDetails.Where(p => string.IsNullOrEmpty(p.PaymentCode)).Select(p => p.DebtDetilId).ToList();
                var debtDetails = await _debtDetailQueryService.GetAllListAsync(p => debtDetilIds.Contains(p.Id), new List<string> { "Debt" });
                var debts = debtDetails.Select(p => p.Debt).Distinct().ToList();
                var debtBillCodes = debts.Select(p => p.BillCode).Distinct().ToList();
                var debtAlls = await _db.Debts.Include(p => p.DebtDetails).Where(p => debtBillCodes.Contains(p.BillCode)).ToListAsync();
                var debtCodes_Difference = debts.Where(p => p.BusinessDeptId != paymentAuto.BusinessDeptId || p.CompanyId != paymentAuto.CompanyId).Select(p => p.BillCode).Distinct().ToList();
                var bankInfos = await _paymentAutoAgent.GetBankInfoByPaymentAutoItemId(id);
                if (debtCodes_Difference.Any() && debtCodes_Difference.Count() > 0)
                {
                    throw new ApplicationException($"操作失败，原因：[{string.Join(',', debtCodes_Difference)}]与批量付款单核算部门或公司不一致，不能跨组织发起付款，请重新选择付款明细！");
                }

                var detailAgentLimits = paymentAuto.PaymentAutoDetails.Where(p => p.LimitedDiscount != null && p.LimitedDiscount != 0).Select(o => o.DebtDetilId);
                if (detailAgentLimits.Any())
                {
                    var detailAgentLimitList = debtDetails.Where(p => detailAgentLimits.Contains(p.Id))?.Select(o => o.Debt?.AgentId).Distinct().ToHashSet();
                    var bankAttchList = bankInfos.Where(p => string.IsNullOrEmpty(p.AttachFileIds) && detailAgentLimitList.Contains(p.AgentId))?.Select(o => o.AgentName).ToHashSet();
                    if (bankAttchList != null && bankAttchList.Count() > 0)
                    {
                        throw new ApplicationException($"操作失败，原因：{string.Join(',', bankAttchList)}供应商的付款明细设置了现金折扣金额，请上传现金折扣附件！");
                    }
                }

                var abatements1 = await _db.Abatements.Where(p => debtBillCodes.Contains(p.CreditBillCode)).ToListAsync();
                var abatements2 = await _db.Abatements.Where(p => debtBillCodes.Contains(p.DebtBillCode)).ToListAsync();
                var abatements = abatements1.Union(abatements2).ToList();

                var errorCodesGT = new List<string>();
                var errorCodesLT0 = new List<string>();
                var otherPaymentAutoDetails = await _db.PaymentAutoDetails
                    .Include(p => p.PaymentAutoItem)
                    .Include(p => p.DebtDetail)
                    .Where(p => p.PaymentAutoItemId != paymentAuto.Id && p.PaymentAutoItem.Status != PaymentAutoItemStatusEnum.Completed)
                    .Select(p => new
                    {
                        p.DebtDetilId,
                        p.DebtDetail.Value,
                        p.DebtDetail.DebtId
                    }).ToListAsync();
                foreach (var item in debtAlls)
                {
                    var otherSumValue = otherPaymentAutoDetails.Where(p => p.DebtId == item.Id).Sum(p => p.Value);
                    var abatementValue = abatements.Where(p => p.DebtBillCode == item.BillCode || p.CreditBillCode == item.BillCode).Sum(p => p.Value);
                    var remianValue = item.Value - abatementValue - otherSumValue;

                    var needPayDebtDetails = debtDetails.Where(p => p.DebtId == item.Id).ToList();
                    var needPayValue = needPayDebtDetails.Sum(p => p.Value);
                    if (remianValue < needPayValue)
                    {
                        errorCodesGT.Add(item.BillCode);
                    }
                    var debtAll = debtAlls.Where(p => p.BillCode == item.BillCode).FirstOrDefault();
                    if (debtAll != null)
                    {
                        if (debtAll.DebtDetails.Where(p => p.Value < 0).Count() > 0)
                        {
                            errorCodesLT0.Add(item.BillCode);
                        }
                    }
                }
                var getContractDelayInfoListInput = new GetContractDelayInfoListInput();
                var debtAllGroups = debtAlls.GroupBy(p => new { p.AgentId }).ToList();
                foreach (var debtAllGroup in debtAllGroups)
                {
                    getContractDelayInfoListInput.ContractDelayList.Add(new ContractDelayList
                    {
                        AgentId = debtAllGroup.Key.AgentId,
                        ProjectIds = debtAllGroup.ToList().Where(p => p.ProjectId.HasValue).Select(p => p.ProjectId).Distinct().ToList()
                    });
                }
                getContractDelayInfoListInput.CompanyId = paymentAuto.CompanyId;
                if (getContractDelayInfoListInput.ContractDelayList.Count() > 0)
                {
                    var pmret = await _projectApiExcuteClient.GetContractDelayInfoList(getContractDelayInfoListInput);
                    if (!pmret.Data)
                    {
                        throw new ApplicationException($"{pmret.Message}项目协议超期未回签，请尽快完成项目协议回签任务！");
                    }
                }
                await CheckNonPassStoreHouseValid(debtAlls);
                if (errorCodesGT.Count() > 0)
                {
                    throw new ApplicationException($"操作失败，原因：[{string.Join(',', errorCodesGT)}]应付单本次付款金额大于可付款金额，请重新选择付款明细！");
                }
                if (errorCodesLT0.Count() > 0)
                {
                    throw new ApplicationException($"操作失败，原因：[{string.Join(',', errorCodesLT0)}]应付中存在负数的付款明细，请联系运维进行数据处理！");
                }

                //销售账期查询是否含有运行中寄售垫资
                var saleDebrDetails = debtDetails.Where(x => x.AccountPeriodType == 2).ToList();
                var creditIds = saleDebrDetails.Select(x => x.CreditId).ToList();
                var abas = await (from aba in _db.AdvanceBusinessApply
                                  join c in _db.Credits on aba.CompanyId equals c.CompanyId
                                  where aba.HospitalId == c.CustomerId && aba.ServiceId == c.ServiceId && creditIds
                           .Contains(c.Id) && aba.Status == 2
                                  select aba).ToListAsync();
                var haveAdvancePayment = abas != null && abas.Any() ? 1 : 0;

                var zoreDetails = debtDetails.Where(p => p.Value == 0).ToList();
                if (zoreDetails != null && zoreDetails.Count() > 0)
                {
                    throw new ApplicationException($"，应付单号{string.Join(",", zoreDetails.Select(p => p.Debt.BillCode))}本次付款金额为0，请删除后操作！");
                }
                var debtPKIds = debtDetails.Select(x => x.DebtId);
                var debtlists = await (from d in _db.Debts where debtPKIds.Contains(d.Id) && (d.DebtType == DebtTypeEnum.revise || d.DebtType == DebtTypeEnum.order) select d).ToListAsync();
                if (debtlists.Count > 0 && false)
                {
                    #region 修订应付和购货应付（寄售）这两种类型的付款，检查进项票金额是否大于等于他的和
                    var codes = debtDetails.Where(x => x.Debt.DebtType == DebtTypeEnum.revise || x.Debt.DebtType == DebtTypeEnum.order).Select(x => x.PurchaseCode).ToList();
                    var inputBillSubmitDetails = await (from ibsd in _db.InputBillSubmitDetails
                                                        join ibs in _db.InputBills on ibsd.InputBillId equals ibs.Id
                                                        where codes.Contains(ibsd.StoreInItemCode) && ibs.Status == 2
                                                        select ibsd).ToListAsync();
                    string errorMsg = string.Empty;
                    foreach (var debt in debtDetails)
                    {
                        var debtSingle = debtlists.FirstOrDefault(x => x.Id == debt.DebtId);
                        if (debtSingle == null)
                        {
                            continue;
                        }
                        var singleValue = inputBillSubmitDetails.Where(x => x.StoreInItemCode == debt.PurchaseCode).Sum(x => x.NoTaxAmount);
                        if ((debtSingle.DebtType == DebtTypeEnum.revise || debtSingle.DebtType == DebtTypeEnum.order) && debt.Value > singleValue)
                        {
                            errorMsg += debt.Debt.BillCode + "，";
                        }
                    }
                    if (!string.IsNullOrEmpty(errorMsg))
                    {
                        errorMsg = errorMsg.Substring(0, errorMsg.Length - 1);
                        throw new ApplicationException(string.Concat("操作失败，原因：批量付款单号【", paymentAuto.Code, "】，应付单号【", errorMsg, "】进项发票到票金额不足！"));
                    }
                    #endregion
                }

                var isOperateApproval = 0;
                var unionAuditProjectCode = ""; //联合审批项目单号
                var debtProjectIds = new List<Guid>();
                if (debtDetails != null && debtDetails.Any())
                {
                    var purchaseCodes = debtDetails.Select(p => p.PurchaseCode).ToList();
                    if (purchaseCodes != null)
                    {
                        var Purchases = await _purchaseApiClient.GetList(new PurchaseDataInput { Codes = purchaseCodes });
                        if (Purchases != null && Purchases.List != null && Purchases.List.Any())
                        {
                            var noComplete = Purchases.List.Where(p => p.OAInfo != null && p.OAInfo.CurrentOAStepName != "归档").ToList();
                            if (noComplete != null && noComplete.Any())
                            {
                                throw new ApplicationException($"操作失败，原因：采购单号{string.Join(",", noComplete.Select(p => p.Code))}合同尚未完成，不能付款.");
                            }
                        }
                    }

                    var acceptancePeriods = debtDetails.Where(p => p.AccountPeriodType == 4 && !string.IsNullOrEmpty(p.PurchaseCode)).ToList(); //验收账期
                    if (acceptancePeriods != null && acceptancePeriods.Any())
                    {
                        foreach (var detail in acceptancePeriods)
                        {
                            var equipRet = await _equipmentsApiClient.EquipIsAllFinishByPurchaseOrderCodeAsync(new DTOs.Equipments.PurchaseOrderCodeInput
                            {
                                data = detail.PurchaseCode
                            });
                            if (equipRet.Data != true)
                            {
                                throw new ApplicationException($"操作失败，原因：【验收账期】采购单号{detail.PurchaseCode}下设备清单尚未完全验收，不能付款.");
                            }
                        }
                    }

                    var warrantyPeriods = debtDetails.Where(p => p.AccountPeriodType == 5).ToList(); //质保账期
                    if (warrantyPeriods != null && warrantyPeriods.Any())
                    {
                        var warrantyPeriods_projectIds = warrantyPeriods.Where(p => p.Debt != null && p.Debt.ProjectId.HasValue).Select(p => p.Debt.ProjectId.Value).ToList();
                        var presales = await _sellApiClient.GetPreSaleListForProjectAsync(new DTOs.Sell.QueryPreSaleListForProjectInput
                        {
                            ProjectIds = warrantyPeriods_projectIds
                        });
                        if (presales != null && presales.Any())
                        {
                            var saleNos = new List<string>();
                            foreach (var preSale in presales)
                            {
                                saleNos.AddRange(preSale.SaleList.Select(p => p.BillCode));
                            }
                            saleNos = saleNos.Distinct().ToList();
                            var credits = await _db.Credits.Where(p => saleNos.Contains(p.OrderNo) && p.AbatedStatus == AbatedStatusEnum.NonAbate).ToListAsync();
                            if (credits != null && credits.Any())
                            {
                                throw new ApplicationException($"操作失败，原因：【质保账期】应收单号{string.Join(",", credits.Select(p => p.BillCode))}未完全冲销，不能付款.");
                            }
                        }
                    }

                    var salePeriods = debtDetails.Where(p => p.AccountPeriodType == 2).ToList(); //销售账期
                    if (salePeriods != null && salePeriods.Any())
                    {
                        var errMsg = await Verifiy(salePeriods);
                        if (!string.IsNullOrEmpty(errMsg))
                        {
                            throw new ApplicationException($"应收单号{errMsg}");
                        }
                    }
                    var agentNames = debts.GroupBy(p => p.AgentName).Select(p => p.Key).ToList();
                    var agentIds = debts.GroupBy(p => p.AgentId).Select(p => p.Key).ToList();

                    foreach (var agentName in agentNames)
                    {
                        var temp = debts.Where(p => p.AgentName == agentName).ToList();
                        if (temp != null && temp.Count() > 1)
                        {
                            var coinNameCount = temp.Select(p => p.CoinName).Distinct().Count();
                            if (coinNameCount > 1)
                            {
                                throw new ApplicationException($"操作失败，原因:【{agentName}】存在{string.Join("、", temp.Select(p => p.CoinName).Distinct())}币种,请保持统一币种付款！");
                            }
                        }
                    }

                    var projectIds = debts.Where(p => p.DebtType != DebtTypeEnum.selforder && p.ProjectId.HasValue).Select(p => new { p.ProjectId, p.ProjectName }).Distinct().ToList();
                    var dictionaryOutputs = await _bDSApiClient.GetDataDictionaryListByType("IVDBusinessDeptUnionAudit");
                    var omInput = new FinanceCheckPayQuotaInput();
                    if (projectIds != null && projectIds.Any())
                    {
                        foreach (var projectId in projectIds)
                        {
                            if (projectId.ProjectId.HasValue)
                            {
                                debtProjectIds.Add(projectId.ProjectId.Value);
                            }
                            var debtIds = debts.Where(p => p.DebtType != DebtTypeEnum.selforder && p.ProjectId == projectId.ProjectId).Select(p => p?.Id).ToList();
                            var debtDetailIds = debtDetails.Where(p => debtIds.Contains(p.DebtId)).Select(p => p.Id).ToList();
                            omInput.projectInfo.Add(new OM_ProjectInfoInput
                            {
                                projectId = projectId.ProjectId.Value,
                                projectName = projectId.ProjectName,
                                amount = paymentAuto.PaymentAutoDetails.Where(p => debtDetailIds.Contains(p.DebtDetilId)).Sum(p => p.Value)
                                //amount = debts.Where(p => p.ProjectId == projectId).Sum(p => p.RMBAmount ?? p.Value)
                            });

                        }

                        var omRet = await _oMApiClient.CheckCreditPayQuota(omInput);
                        if (omRet.payControlList != null && omRet.payControlList.Count > 0)
                        {
                            foreach (var item in omInput.projectInfo)
                            {
                                var payControll = omRet.payControlList.FirstOrDefault(p => p.projectId.Equals(item.projectId.ToString(), StringComparison.OrdinalIgnoreCase));
                                if (payControll == null)
                                {
                                    break;
                                }

                                if (payControll.controlMode != null && payControll.purchaseQuota == null && payControll.usedAmt == null)
                                {
                                    throw new ApplicationException("操作失败，原因：存在管控待审批项目，请通过审批后再提交.");
                                }

                                if (payControll.controlMode.HasValue && payControll.purchaseQuota.HasValue && payControll.usedAmt.HasValue && payControll.controlMode == 1 && payControll.purchaseQuota - payControll.usedAmt - item.amount < 0)
                                {
                                    throw new ApplicationException($"操作失败，原因：项目[{item.projectName}]金额超出限额，请调整后再提交.");
                                }
                            }
                        }
                    }
                    var projectCodes = debts.Select(p => p.ProjectCode).Distinct().ToList();
                    if (projectCodes != null && projectCodes.Count() > 0)
                    {
                        foreach (var projectCode in projectCodes)
                        {
                            if (dictionaryOutputs.Count() > 0 && dictionaryOutputs.Select(p => p.Attribute).ToList().Contains(projectCode))
                            {
                                unionAuditProjectCode = projectCode;//只要匹配到一个联合审批单号就传过去
                                continue;
                            }
                        }

                    }


                    var detailsAgentQuery = _autoDetailQueryService.GetIQueryable(p => p.PaymentAutoItemId == id, new List<string>() { "DebtDetail.Debt" });
                    var detailAgentCount = await detailsAgentQuery.Select(p => p.DebtDetail.Debt.AgentId).ToListAsync();
                    bankInfos = bankInfos.Where(p => detailAgentCount.Contains(p.AgentId)).ToList();
                    if (bankInfos == null || !bankInfos.Any())
                    {
                        throw new ApplicationException("操作失败，原因:未查询到匹配的银行信息");
                    }

                    var validBankCount = bankInfos.Where(p => string.IsNullOrEmpty(p.AccountName) || string.IsNullOrEmpty(p.Account) || string.IsNullOrEmpty(p.BankName) || string.IsNullOrEmpty(p.BankCode));
                    if (validBankCount.Count() > 0)
                    {
                        throw new ApplicationException($"操作失败，原因:请完善供应商{string.Join(',', validBankCount.Select(o => o.AgentName))}的付款方式和账号");
                    }
                    //if (bankInfos.Where(p => !string.IsNullOrEmpty(p.AccountName) && !string.IsNullOrEmpty(p.Account) && !string.IsNullOrEmpty(p.BankName) && !string.IsNullOrEmpty(p.BankCode)).Count(p => agentIds.Contains(p.AgentId)) != detailAgentCount.Distinct().Count())
                    //{
                    //    throw new ApplicationException("操作失败，原因:提交前请确认每个供应商的付款方式和账号都是完善的");
                    //}
                }
                var agentBanks = await _db.PaymentAutoAgentBankInfos.Where(p => p.PaymentAutoItemId == id).AsNoTracking().ToListAsync();
                if (agentBanks.Count(p => p.Paymentabroad == 1) > 0 && agentBanks.Count(p => !p.Paymentabroad.HasValue || p.Paymentabroad == 0) > 0)
                {
                    throw new ApplicationException("操作失败，原因:境外付款业务和国内付款业务不能同时提交");
                }
                var canSubmitStatusEnums = new List<int> { (int)PaymentAutoItemStatusEnum.WaitSubmit };
                if (!canSubmitStatusEnums.Contains((int)paymentAuto.Status))
                {
                    throw new ApplicationException("操作失败，原因:只有[草稿]状态的批量付款可提交审批");
                }

                int paymentAutoDebtType = 0;
                if (debtDetails != null && debtDetails.Any())
                {
                    if (debtDetails.Any(p => p.Debt != null && p.Debt.DebtType == DebtTypeEnum.selforder))
                    {
                        paymentAutoDebtType = 1;
                    }
                    else if (debtDetails.Any(p => p.Debt != null && p.Debt.DebtType == DebtTypeEnum.order) && debtDetails.Count(p => p.Debt != null && p.Debt.DebtType == DebtTypeEnum.order) == debtDetails.Count(p => p.Debt != null))
                    {
                        paymentAutoDebtType = 2;
                    }

                    if (debtDetails.Any(p => p.Debt != null && p.Debt.BillCode!.Contains("-POR-", StringComparison.CurrentCultureIgnoreCase)))
                    {
                        paymentAutoDebtType = 2;
                    }
                }
                var oaAgentName = DealWithPaymentAutoAgent(agentBanks);

                var input = new WeaverInput
                {
                    BaseInfo = new BaseInfo
                    {
                        Operator = paymentAuto.CreatedBy,
                        RequestName = $"【批量付款申请】[{paymentAuto.Code}]-{paymentAuto.CompanyName}{oaAgentName}",
                        Remark = paymentAuto.Remark,
                    },
                    MainData = new MainData
                    {
                        IsOnlyReturnAccountPeriod = debtDetails.Count() == debtDetails.Count(p => p.AccountPeriodType == 0) && debtDetails.Count(p => p.AccountPeriodType == 0) > 0 ? 1 : 0,
                        Business_id = paymentAuto.Id.ToString(),
                        FCreatorID = paymentAuto.CreatedBy,
                        Iframe_link = $"{_configuration["BaseUri"]}/fam/financeManagement/BulkPayment/indexOA?id={id}", //PC的Iframe地址,
                        Height_m = 280,
                        Iframe_link_m = $"{_configuration["BaseUri"].Replace("/v1", "")}/oamobile/#/batchPaymentEmbedPage?id={id}",
                        fkje = paymentAuto.PaymentAutoDetails.Sum(p => p.Value),
                        IsOperateApproval = isOperateApproval,
                        CpDepartment = paymentAuto.BusinessDeptId,
                        CPcompanyCode = paymentAuto.NameCode,
                        ProjectCode = unionAuditProjectCode,//联合审批项目单号 
                        HaveAdvancePayment = haveAdvancePayment,
                        PaymentAutoDebtType = paymentAutoDebtType,
                        ProjectLeader = await GetProjectLeader(debtProjectIds),
                    },
                    OtherParams = new OtherParams
                    {
                        IsNextFlow = 1,
                    }
                };
                WeaverOutput<WeaverOutputData> oaResult;
                if (string.IsNullOrEmpty(paymentAuto.OARequestId))
                {
                    oaResult = await _weaverApiClient.CreateWorkFlow(input, WorkFlowCode.BatchPaymentForm);
                }
                else
                {
                    input.BaseInfo.RequestId = int.Parse(paymentAuto.OARequestId);
                    oaResult = await _weaverApiClient.SubmitWorkFlow(input, WorkFlowCode.BatchPaymentForm);
                }
                if (!oaResult.Status)
                {
                    throw new ApplicationException(oaResult.Msg);
                }
                if (oaResult.Data == null || oaResult.Data.Requestid <= 0)
                {
                    throw new ApplicationException("发起OA流程请求失败");
                }
                paymentAuto.Status = PaymentAutoItemStatusEnum.Auditing;
                paymentAuto.UpdatedBy = userName;
                paymentAuto.UpdatedTime = DateTimeOffset.UtcNow;
                paymentAuto.OARequestId = oaResult.Data.Requestid.ToString();

                await _paymentAutoItemRepository.UpdateAsync(paymentAuto);
                var res = await _unitOfWork.CommitAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }

        private async Task CheckNonPassStoreHouseValid(List<DebtPo> debtAlls)
        {
            var configs = await _daprClient.GetConfiguration("appconfig-default", new[] { "HospitalDeliveryNonPassStoreHouseValidSwitch" });
            if (configs != null && configs.Items != null && configs.Items.Count > 0)
            {
                var result = JsonConvert.DeserializeObject<HospitalDeliveryNonPassStoreHouseValidSwitchDto>(configs.Items["HospitalDeliveryNonPassStoreHouseValidSwitch"].Value);
                if (result != null && result.FinanceIsOpen)
                {
                    var debtAllGroupsCompanyAgentId = debtAlls.GroupBy(p => new { p.AgentId, p.CompanyId }).ToList();
                    var waybillInfoDetailsInput = new WaybillInfoDetailsInput
                    {
                        WaybillInfoDetails = new List<WaybillInfoDetailsInputData>()
                    };
                    foreach (var debtAllGroup in debtAllGroupsCompanyAgentId)
                    {
                        waybillInfoDetailsInput.WaybillInfoDetails.Add(new WaybillInfoDetailsInputData
                        {
                            AgentId = debtAllGroup.Key.AgentId,
                            CompanyId = debtAllGroup.Key.CompanyId,
                        });
                    }
                    if (waybillInfoDetailsInput.WaybillInfoDetails.Count() > 0)
                    {
                        var pmret = await _iInventoryApiClient.WaybillInfoValidForFinance(waybillInfoDetailsInput);
                        if (pmret.WaybillInfoDetails != null && pmret.WaybillInfoDetails.Any())
                        {
                            var waybillInfoDetailsTemp = pmret.WaybillInfoDetails.Where(p => p.WaybillInfoTag.HasValue && !p.WaybillInfoTag.Value).ToList();
                            if (waybillInfoDetailsTemp.Any())
                            {
                                throw new ApplicationException($"操作失败，原因：当前供应商：{string.Join(",", waybillInfoDetailsTemp.Select(p => p.AgentName))},存在未提交随货同行单：{string.Join(",", waybillInfoDetailsTemp.SelectMany(p => p.InvalidStoreInCodes))}，无法发起付款，请先完成随货同行单再提交；谢谢！");
                            }
                        }
                    }
                }
            }
        }

        private string DealWithPaymentAutoAgent(List<PaymentAutoAgentBankInfoPo> agentBanks)
        {
            string result = string.Empty;
            int count = agentBanks.Count;
            if (count > 0)
            {
                result = "-" + agentBanks.FirstOrDefault()?.AgentName;
            }
            if (count > 1)
            {
                result += "等";
            }
            return result;
        }

        private async Task<string> GetProjectLeader(List<Guid> projectIds)
        {
            string result = "";
            if (projectIds.Distinct().Count() == 1)
            {
                var projectleaders = await _projectMgntApiClient.GetProjectLeader(projectIds);
                var employeeIds = projectleaders.Select(x => x.leaderId).ToList();
                var employeeList = await _uCApiClient.GetUserListByEmployeeIds(employeeIds);
                result = employeeList.FirstOrDefault(w => employeeIds.Contains(w.employeeId) && !w.name.StartsWith("xn") && !string.IsNullOrEmpty(w.oaAccount))?.oaAccount ?? "";
            }
            return result;
        }

        /// <summary>
        /// 批量付款（发票入账新增）校验
        /// </summary>
        /// <param name="detailPos"></param>
        /// <returns></returns>
        private async Task<string> Verifiy(List<DebtDetailPo> detailPos)
        {
            string errMsg = string.Empty;
            var errList = new List<string>();
            var debtPKIds = detailPos.Select(x => x.DebtId);
            //查询初始应付
            var debtlists = await (from d in _db.Debts where debtPKIds.Contains(d.Id) && (d.DebtType == DebtTypeEnum.origin) select d).ToListAsync();

            var creditIds = detailPos.Select(x => x.CreditId).ToHashSet();
            //查询发票入账已审核发票集合
            var invoiceReceiptItems = await _db.InvoiceReceiptItem.Where(x => 1 == 1).ToListAsync();
            var invoiceReceiptDetails = await _db.InvoiceReceiptDetail.Where(x => 1 == 1).ToListAsync();
            //垫资单集合
            var abas = await _db.AdvanceBusinessApply.Where(x => 1 == 1).ToListAsync();

            //1.销售账期是否开发票，没开发票不让提交
            var invoiceCredits = await _db.InvoiceCredits.Where(x => creditIds.Contains(x.CreditId)).ToListAsync();
            var credits = _db.Credits.Where(x => creditIds.Contains(x.Id)).Distinct().ToList();
            foreach (var creditId in creditIds)
            {
                var credit = credits.FirstOrDefault(x => x.Id == creditId);
                if (credit != null)
                {
                    var single = invoiceCredits.FirstOrDefault(x => x.CreditId == creditId);
                    // #89079
                    if (single == null && (!credit.IsNoNeedInvoice.HasValue || credit.IsNoNeedInvoice == IsNoNeedInvoiceEnum.Need))
                    {
                        //未开发票
                        errList.Add($"【{credit.BillCode}】未开票！");
                        continue;
                    }
                    //2.根据应收单上业务单元、公司、客户校验AdvanceBusinessApply垫资单是否有数据，无数据提交
                    var aba = abas.Where(x => x.CompanyId == credit.CompanyId && x.HospitalId == credit.CustomerId && x.ServiceId == credit.ServiceId).OrderByDescending(x => x.BillDate).FirstOrDefault();
                    if (aba == null)
                    {
                        continue;
                    }
                    //垫资单状态是运行中或已完成不可以提交，反之可以提交，如果找到，垫资单上的是否发票入账如果是否，也可以提交
                    if (aba.IsInvoice && (!credit.IsNoNeedInvoice.HasValue || credit.IsNoNeedInvoice == IsNoNeedInvoiceEnum.Need))
                    {
                        //3.有数据则校验对应的（业务单元、公司、客户）发票入账是否已审核
                        if (aba.Status == 2)
                        {
                            foreach (var item in detailPos)
                            {
                                if (item.IsInvoiceReceipt.HasValue && item.IsInvoiceReceipt.Value)
                                {
                                    continue;
                                }
                                else
                                {
                                    if (item.CreditId == credit.Id)
                                    {
                                        errList.Add($"【{credit.BillCode}】未入账！");
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    //查询是否为初始应付，初始应付找不到应收直接跳过验证
                    var ids = detailPos.Where(x => x.CreditId.HasValue && x.CreditId == creditId).Select(x => x.DebtId).Distinct().ToList();
                    var others = debtlists.Where(x => ids.Contains(x.Id)).ToList();
                    if (others.Count != ids.Count)
                    {
                        errList.Add($"【{creditId}】未找到！");
                    }
                }
            }

            errMsg = errList.Any() ? string.Join(",", errList.Distinct()) : string.Empty;
            return errMsg;
        }

        /// <summary>
        /// 驳回
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task RejectAsync(Guid id)
        {
            _logger.LogInformation($"批量付款审核驳回开始[{id}]");
            var payment = await _paymentAutoItemRepository.GetWithNoTrackAsync(id);
            if (payment == null)
            {
                throw new ApplicationException($"Unable to find {id}");
            }
            if (payment.Status == PaymentAutoItemStatusEnum.Auditing)
            {
                payment.Status = PaymentAutoItemStatusEnum.WaitSubmit;
            }
            payment.UpdatedTime = DateTime.Now;
            await _paymentAutoItemRepository.UpdateAsync(payment);
            //删除oa流程
            var oaresult = await _weaverApiClient.DelWorkFlow(payment.CreatedBy, Convert.ToInt32(payment.OARequestId));
            await _unitOfWork.CommitAsync();
            _logger.LogWarning(oaresult?.ToJson());
            _logger.LogInformation($"批量付款审核驳回结束[{id}]");
        }

        /// <summary>
        /// 领导审批通过
        /// </summary>
        /// <param name="id"></param>
        /// <param name="auditor"></param>
        /// <returns></returns>
        public async Task LeaderAuditAsync(Guid id, string auditor)
        {
            _logger.LogInformation($"批量付款领导审批通过开始[{id}]");
            var payment = await _paymentAutoItemRepository.GetWithNoTrackAsync(id);
            if (payment == null)
            {
                throw new ApplicationException($"Unable to find {id}");
            }
            payment.OAAuditor = auditor;
            payment.OAAuditTime = DateTime.Now;
            payment.UpdatedTime = DateTime.Now;

            await _paymentAutoItemRepository.UpdateAsync(payment);
            await _unitOfWork.CommitAsync();
            _logger.LogInformation($"批量付款领导审批通过结束[{id}]");
        }

        /// <summary>
        /// 归档
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task CompleteAsync(Guid id)
        {
            _logger.LogInformation($"批量付款归档开始[{id}]");
            var payment = await _paymentAutoItemRepository.GetWithNoTrackAsync(id);
            if (payment == null)
            {
                throw new ApplicationException($"Unable to find {id}");
            }
            try
            {
                var details = await _bulkPaymentQueryService.GetPaymentDetails(new QueryById { Id = id });
                var agentGroups = details.GroupBy(t => new { t.AgentId })
                     .Select(t => new KingdeePayApplyDetail { AgentId = t.Key.AgentId ?? default })
                     .ToList();
                var bankInfos = await _paymentAutoAgent.GetBankInfoByPaymentAutoItemId(id);
                var inputKD = new KingdeePayApplyDto()
                {
                    applyPeopleNumber = payment.CreatedBy,
                    applyDetail = new List<KingdeePayApplyDetail>(),
                    payOrgNumber = payment.NameCode,
                    payNumber = payment.Code,
                    payType = "201",
                    businessOrg = payment.BusinessDeptId ?? "",
                    remark = payment.Remark ?? "批量付款",

                };

                var debtDetilIds = payment.PaymentAutoDetails.Select(p => p.DebtDetilId);
                var debtDetils = await _debtDetailQueryService.GetAllListAsync(p => debtDetilIds.Contains(p.Id), new List<string> { "Debt" });
                var debts = debtDetils.Select(p => p.Debt).ToList();
                var projectIds = debtDetils.Select(p => p.Debt.ProjectId.Value).Distinct().ToList();
                var projects = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
                foreach (var item in agentGroups)
                {
                    var bank = bankInfos.Where(t => t.AgentId == item.AgentId).First();
                    var details_agent = details.Where(t => t.AgentId == item.AgentId)
                        .Select(t => new ApplyDetailData
                        {
                            detailNumber = t.DebtDetailId.ToString().ToUpper(),
                            gaterAmount = t.DebtDetailValue,
                            settlementModel = string.IsNullOrEmpty(bank.PayClassify) ? "" : bank.PayClassify.Trim(),
                            orderNumber = debtDetils.FirstOrDefault(p => p.Id == t.DebtDetailId)?.Debt?.OrderNo,
                            projectNumber = debtDetils.FirstOrDefault(p => p.Id == t.DebtDetailId)?.Debt?.ProjectId.ToString(),
                            receivableNumber = t.DebtBillCode,
                            cashDiscount = t.LimitedDiscount,
                        }).ToList();
                    foreach (var subitem in details_agent)
                    {
                        var projectId = Guid.Parse(subitem.projectNumber);
                        var project = projects.Where(p => p.Id == projectId).FirstOrDefault();
                        if (project != null)
                        {
                            subitem.projectNumber = project.Code;
                        }
                        else
                        {
                            throw new Exception($"项目Id[{projectId}],没有该项目");
                        }
                    }
                    var debt = debts.Where(p => p.AgentId == item.AgentId).FirstOrDefault();
                    var payApplyDetail = new KingdeePayApplyDetail
                    {
                        AgentId = item.AgentId,
                        applyDetailData = details_agent,
                        bankAccount = bank.Account ?? "",
                        bankName = bank.AccountName ?? "",
                        bankBranchName = bank.BankName ?? "",
                        bankBranchNumber = bank.BankCode ?? "",
                        transferDiscourse = bank.TransferDiscourse,
                        moneyNumber = debt == null || string.IsNullOrEmpty(debt.CoinCode) ? "CNY" : debt.CoinCode,//默认人名币
                    };
                    inputKD.applyDetail.Add(payApplyDetail);

                    if (bank.Paymentabroad.HasValue && bank.Paymentabroad.Value == 1) //境外支付
                    {
                        inputKD.importGoods = bank.importGoods;
                        inputKD.costBearingParty = bank.CostBearingParty;
                        inputKD.jfzx_ynpush = bank.Ynpush.HasValue ? bank.Ynpush.ToString() : "";
                        inputKD.jfzx_postscript = bank.Postscript;
                        inputKD.jfzx_transactioncoding = bank.Transactioncoding;
                        inputKD.jfzx_paymentabroad = bank.Paymentabroad.HasValue ? bank.Paymentabroad.ToString() : "";
                        inputKD.jfzx_contractno = bank.Contractno;
                        inputKD.jfzx_invoiceno = bank.InvoiceNo;
                    }
                }
                inputKD.applyDetail = inputKD.applyDetail.Distinct().ToList();

                // 从PaymentAutoItem中获取OARequestId
                var paymentItem = await _paymentAutoItemRepository.GetWithNoTrackAsync(id);
                inputKD.requestId = paymentItem?.OARequestId;

                var res = await _kingdeeApiClient.PushPaymentApplyToKingdee(inputKD);
                if (res.Code == CodeStatusEnum.Failed)
                {
                    _logger.LogError($"批量付款归档[{id}],调用金蝶接口错误：{res.Message}");
                    throw new Exception($"批量付款归档[{id}],调用金蝶接口错误：{res.Message}");

                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"批量付款归档[{id}],错误：{ex.Message}");
                throw new Exception($"批量付款归档[{id}],错误：{ex.Message}");
            }
            payment.Status = PaymentAutoItemStatusEnum.WaitExecute;
            payment.UpdatedTime = DateTime.Now;

            await _paymentAutoItemRepository.UpdateAsync(payment);
            await _unitOfWork.CommitAsync();
            _logger.LogInformation($"批量付款归档结束[{id}]");
        }
        #endregion
        public async Task<bool> ExcutePaymentAutoItemAsync(Guid id)
        {
            var item = await _paymentAutoItemRepository.GetWithNoTrackAsync(id);
            if (item == null)
            {
                throw new AppServiceException("批量付款单不存在，请刷新页面!");
            }

            var lstDebtDetailId = item.PaymentAutoDetails.Select(x => x.DebtDetilId).ToList();

            var lstDebtDetail = await _debtDetailRepository.GetListAsync(lstDebtDetailId);

            lstDebtDetail = lstDebtDetail.Where(t => t.Status == Domain.DebtDetailStatusEnum.WaitExecute).ToList();

            if (lstDebtDetail.Count <= 0)
            {
                throw new AppServiceException("没有可执行的明细数据！");
            }
            var lstPushDetail = lstDebtDetail.Adapt<List<DebtDetailInput>>();
            var res = await _kingdeeApiClient.PushDebtDetailAsync(lstPushDetail);
            return res;

        }

        public async Task<BaseResponseData<int>> CreatePaymentAutoBankInfos(List<AgentBankInput> agentBanks)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            if (agentBanks != null && agentBanks.Any())
            {
                var bankInfos = agentBanks.Adapt<List<PaymentAutoAgentBankInfo>>();
                ret = await _paymentAutoAgent.CreateBankInfos(bankInfos);
            }
            else
            {
                ret = BaseResponseData<int>.Failed(500, "操作失败：供应商数据为空");
            }
            return ret;
        }

        public async Task<BaseResponseData<int>> SetAgentBank(AgentBankInput input)
        {
            if (input == null)
            {
                return BaseResponseData<int>.Failed(500, "操作失败，原因：参数为空");
            }
            if (string.IsNullOrEmpty(input.PayClassify))
            {
                return BaseResponseData<int>.Failed(500, "操作失败，原因：请选择付款方式");
            }
            if (string.IsNullOrEmpty(input.Account))
            {
                return BaseResponseData<int>.Failed(500, "操作失败，原因：请选择收款账号");
            }
            if (string.IsNullOrEmpty(input.AccountName))
            {
                return BaseResponseData<int>.Failed(500, "操作失败，原因：请选择收款账号");
            }
            if (string.IsNullOrEmpty(input.BankCode))
            {
                return BaseResponseData<int>.Failed(500, "操作失败，原因：请选择收款账号");
            }
            if (string.IsNullOrEmpty(input.BankName))
            {
                return BaseResponseData<int>.Failed(500, "操作失败，原因：请选择收款账号");
            }
            if (input.Type.HasValue && input.Type.Value == 2)
            {

            }
            var paymentAutoAgentBank = await _paymentAutoAgentBankInfoQueryService.FirstOrDefaultAsync(p => p.PaymentAutoItemId == input.PaymentAutoItemId && p.AgentId == input.AgentId);

            if (paymentAutoAgentBank != null)
            {
                paymentAutoAgentBank.Account = input.Account;
                paymentAutoAgentBank.AccountName = input.AccountName;
                paymentAutoAgentBank.BankCode = input.BankCode;
                paymentAutoAgentBank.BankName = input.BankName;
                paymentAutoAgentBank.Contractno = input.Contractno;
                paymentAutoAgentBank.CostBearingParty = input.CostBearingParty;
                paymentAutoAgentBank.importGoods = input.importGoods;
                paymentAutoAgentBank.InvoiceNo = input.InvoiceNo;
                paymentAutoAgentBank.PayClassify = input.PayClassify;
                paymentAutoAgentBank.Paymentabroad = input.Type.HasValue && input.Type.Value == 2 ? 1 : 0;
                paymentAutoAgentBank.Postscript = input.Postscript;
                paymentAutoAgentBank.Transactioncoding = input.Transactioncoding;
                paymentAutoAgentBank.TransferDiscourse = input.TransferDiscourse;
                paymentAutoAgentBank.Ynpush = input.Ynpush;
                _db.PaymentAutoAgentBankInfos.Update(paymentAutoAgentBank);

            }
            else
            {
                paymentAutoAgentBank = new PaymentAutoAgentBankInfoPo { };
                paymentAutoAgentBank.Id = Guid.NewGuid();
                paymentAutoAgentBank.PaymentAutoItemId = input.PaymentAutoItemId;
                paymentAutoAgentBank.AgentId = input.AgentId;
                paymentAutoAgentBank.AgentName = input.AgentName;
                paymentAutoAgentBank.Account = input.Account;
                paymentAutoAgentBank.AccountName = input.AccountName;
                paymentAutoAgentBank.BankCode = input.BankCode;
                paymentAutoAgentBank.BankName = input.BankName;
                paymentAutoAgentBank.Contractno = input.Contractno;
                paymentAutoAgentBank.CostBearingParty = input.CostBearingParty;
                paymentAutoAgentBank.importGoods = input.importGoods;
                paymentAutoAgentBank.InvoiceNo = input.InvoiceNo;
                paymentAutoAgentBank.PayClassify = input.PayClassify;
                paymentAutoAgentBank.Paymentabroad = input.Type.HasValue && input.Type.Value == 2 ? 1 : 0;
                paymentAutoAgentBank.Postscript = input.Postscript;
                paymentAutoAgentBank.Transactioncoding = input.Transactioncoding;
                paymentAutoAgentBank.TransferDiscourse = input.TransferDiscourse;
                paymentAutoAgentBank.Ynpush = input.Ynpush;
                await _db.PaymentAutoAgentBankInfos.AddAsync(paymentAutoAgentBank);
            }
            await _db.SaveChangesAsync();
            return BaseResponseData<int>.Success("操作成功！");
        }

        public async Task<BaseResponseData<int>> UpdateRemark(UpdateRemarkInput input)
        {
            var res = await _paymentAutoItemRepository.UpdateRemark(input.Id, input.Remark);
            return res;
        }

        public async Task<BaseResponseData<int>> UpdateTransferDiscourse(UpdateTransferDiscourseInput input)
        {
            var res = await _paymentAutoItemRepository.UpdateTransferDiscourse(input.Id, input.TransferDiscourse);
            return res;
        }
        /// <summary>
        /// 导入付款明细
        /// </summary>
        /// <param name="fileId"></param>
        /// <param name="paymentAutoItemId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<PaymentExcelDetailOutput>> ExportBatchPaymentDetail(Guid fileId, Guid paymentAutoItemId, string userName)
        {

            var ret = BaseResponseData<PaymentExcelDetailOutput>.Success("操作成功！");
            try
            {
                var codeAndValueDic = new Dictionary<string, decimal>();//单号和金额
                var codeAndMessageDic = new Dictionary<string, string>();//单号和错误信息
                int totalCount = 0;
                int fialCount = 0;
                var item = await _paymentAutoItemRepository.GetWithNoTrackAsync(paymentAutoItemId);
                if (item == null)
                {
                    throw new AppServiceException("数据不存在，请刷新页面!");
                }
                if (item.Status != Domain.PaymentAutoItemStatusEnum.WaitSubmit)
                {
                    throw new AppServiceException("只有待提交状态的批量付款单才能导入数据");
                }

                var existDetailList = await _autoDetailQueryService.GetAllListAsync(t => t.PaymentAutoItemId == paymentAutoItemId);
                var stream = await _fileGatewayClient.GetTempFileStreamAsync(fileId);
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (ExcelPackage package = new ExcelPackage(stream))
                {
                    stream.Close();
                    var worksheet = package.Workbook.Worksheets[0]; //获取第一个sheet 
                    //去掉最后的空白行
                    worksheet.TrimLastEmptyRows();
                    int rows = worksheet.Dimension.End.Row;   //获取worksheet的行数 
                    for (int index = worksheet.Dimension.Start.Row + 1; index <= rows; index++)
                    {
                        var billCode = worksheet.Cells[index, 1].Text.Trim(); //应付单号
                        var value = worksheet.Cells[index, 2].Text.Trim();//金额
                        totalCount++;
                        if (string.IsNullOrEmpty(billCode) || string.IsNullOrEmpty(value))
                        {
                            worksheet.Cells[index, 3].Value = "请输入正确的单号或金额";
                            fialCount++;
                            continue;
                        }
                        if (codeAndValueDic.ContainsKey(billCode))
                        {
                            worksheet.Cells[index, 3].Value = "单号重复输入";
                            fialCount++;
                            continue;
                        }
                        codeAndValueDic[billCode] = Convert.ToDecimal(value);
                    }
                    IQueryable<DebtDetailBulkOutput> baseQuery = (from d in _db.DebtDetails
                                                                  join c in _db.Credits on d.CreditId equals c.Id into temp
                                                                  from t in temp.DefaultIfEmpty()
                                                                  join i in _db.Debts on d.DebtId equals i.Id
                                                                  where d.Status == DebtDetailStatusEnum.WaitExecute
                                                                  && d.AccountPeriodType == (int)AccountPeriodTypeEnum.StoreIn
                                                                  && i.AbatedStatus != AbatedStatusEnum.Abated
                                                                  && codeAndValueDic.Keys.Contains(i.BillCode)
                                                                  && i.Value != 0 && d.Value > 0
                                                                  select new DebtDetailBulkOutput
                                                                  {
                                                                      DebtDetilId = d.Id,
                                                                      DebtId = d.DebtId,
                                                                      Code = d.Code,
                                                                      DebtCode = i.BillCode,
                                                                      RelateCode = i.RelateCode,
                                                                      BillDate = i.BillDate,
                                                                      CompanyName = i.CompanyName,
                                                                      Value = d.Value,
                                                                      OrderNo = t.OrderNo,
                                                                      BusinessDeptId = i.BusinessDeptId,
                                                                  }
                                                            ).AsNoTracking();
                    var debtDetailList = await baseQuery.ToListAsync();
                    var payDetails = await _autoDetailQueryService.GetAllListAsync(t => debtDetailList.Select(p => p.DebtDetilId).Contains(t.DebtDetilId));
                    var pays = await _db.PaymentAutoItems.Where(p => payDetails.Select(p => p.PaymentAutoItemId).Contains(p.Id)).AsNoTracking().ToListAsync();
                    foreach (var key in codeAndValueDic.Keys)
                    {
                        var debtDetail = debtDetailList.Where(p => p.DebtCode == key);
                        if (debtDetail == null || debtDetail.Count() == 0)
                        {
                            codeAndMessageDic[key] = "该应付单不存在或该应付单不为入库账期数据";
                            fialCount++;
                            continue;
                        }
                        if (debtDetail.Count() > 1)
                        {
                            codeAndMessageDic[key] = "存在多条应付单号一致的明细数据，不支持导入";
                            fialCount++;
                            continue;
                        }
                        if (debtDetail.FirstOrDefault().BusinessDeptId != item.BusinessDeptId)
                        {
                            codeAndMessageDic[key] = "批量付款的核算部门和应付的核算部门不一致";
                            fialCount++;
                            continue;
                        }
                        if (debtDetail.FirstOrDefault().Value != Convert.ToDecimal(codeAndValueDic[key]))
                        {
                            codeAndMessageDic[key] = "导入表格的金额和应付付款计划明细金额不一致";
                            fialCount++;
                            continue;
                        }
                        var payDetail = payDetails.Where(p => p.DebtDetilId == debtDetail.FirstOrDefault().DebtDetilId).FirstOrDefault();
                        if (payDetail != null)
                        {
                            var payNoCompleteds = pays.Where(p => p.Id == payDetail.PaymentAutoItemId && p.Status != PaymentAutoItemStatusEnum.Completed).ToList();
                            if (payNoCompleteds != null && payNoCompleteds.Count > 0)
                            {
                                codeAndMessageDic[key] = "该应付存在其他在途的批量付款中";
                                fialCount++;
                                continue;
                            }
                        }
                        if (existDetailList.Exists(t => t.DebtDetilId == debtDetail.FirstOrDefault().DebtDetilId) == true)
                        {
                            //已经存在该付款信息明细做覆盖处理
                            if (item.PaymentAutoDetails.Where(p => p.DebtDetilId == debtDetail.FirstOrDefault().DebtDetilId).FirstOrDefault() == null)
                            {
                                codeAndMessageDic[key] = "覆盖时没有找到原付款信息";
                                fialCount++;
                            }
                            else
                            {
                                item.PaymentAutoDetails.Where(p => p.DebtDetilId == debtDetail.FirstOrDefault().DebtDetilId).FirstOrDefault().Value = Convert.ToDecimal(codeAndValueDic[key]);
                            }
                            continue;
                        }
                        item.AddPaymentAutoDetail(new PaymentAutoDetail()
                        {
                            Value = Convert.ToDecimal(codeAndValueDic[key]),
                            PaymentAutoItemId = paymentAutoItemId,
                            DebtDetilId = debtDetail.FirstOrDefault().DebtDetilId.Value,
                            Id = new Guid(),

                        }, userName);
                    }
                    if (fialCount > 0) //输出错误信息
                    {
                        for (int index = worksheet.Dimension.Start.Row + 1; index <= rows; index++)
                        {
                            var billCode = worksheet.Cells[index, 1].Text.Trim(); //应付单号
                            var errMsg = worksheet.Cells[index, 3].Text.Trim();
                            if (codeAndMessageDic.ContainsKey(billCode) && string.IsNullOrEmpty(errMsg))//如果包含并且上面没有赋值错误信息
                            {
                                worksheet.Cells[index, 3].Value = codeAndMessageDic[billCode];
                            }
                        }
                        worksheet.Column(3).Style.Font.Color.SetColor(Color.Red);
                        //生成错误报告文件
                        MemoryStream msFailReport = new MemoryStream(package.GetAsByteArray());
                        ret = BaseResponseData<PaymentExcelDetailOutput>.Success("导入失败！");
                        ret.Data = new PaymentExcelDetailOutput()
                        {
                            FailReportFileId = await _fileGatewayClient.UploadTempFileContentAsync(msFailReport, "付款明细导入错误数据.xlsx"),
                            FailNumber = fialCount,
                            SuccessNumber = totalCount - fialCount,
                            Total = totalCount,
                        };
                    }
                    else
                    {
                        ret.Data = new PaymentExcelDetailOutput()
                        {
                            FailNumber = 0,
                            SuccessNumber = totalCount,
                            Total = totalCount
                        };
                    }
                }
                await _paymentAutoItemRepository.UpdateAsync(item);
                await _unitOfWork.CommitAsync();
                return ret;
            }
            catch (Exception ex)
            {
                ret = BaseResponseData<PaymentExcelDetailOutput>.Failed(500, "导入失败！错误信息：" + ex.Message);
                return ret;
            }
        }

        /// <summary>
        /// 导出账期数据（协调服务导出）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportAccountPeriodTask(DebtDetailBulkQuery query)
        {
            try
            {
                var tasks = new List<ExportTaskInputDto>();
                var task = new ExportTaskInputDto();
                task.ExportTaskId = string.Empty;
                task.AppId = _configuration["coordinateAppId"].ToString();
                // 查询入参转换为字典
                var queryDic = DtoToDictionary(query);
                var headerDic = new Dictionary<string, object>
                {
                    { "X-Inno-UserId", query.UserId },
                    { "X-Inno-UserName", query.UserName??=string.Empty }
                };
                //模板已配置，可不填
                task.QueryApiUri = string.Empty;
                task.HeaderInfo = headerDic;
                task.QueryParam = queryDic;
                if (query.AccountPeriodType == AccountPeriodTypeEnum.Repayment)
                {
                    task.TemplateCode = "fam_batchPaymentRepaymentTemplate";
                }
                else if (query.AccountPeriodType == AccountPeriodTypeEnum.StoreIn)
                {
                    task.TemplateCode = "fam_batchPaymentStoreInTemplate";
                }
                else if (query.AccountPeriodType == AccountPeriodTypeEnum.Sale)
                {
                    task.TemplateCode = "fam_batchPaymentSaleTemplate";
                }
                else if (query.AccountPeriodType == AccountPeriodTypeEnum.ProbablyPay)
                {
                    task.TemplateCode = "fam_batchPaymentProbablyPayTemplate";
                }
                else if (query.AccountPeriodType == AccountPeriodTypeEnum.AcceptancePeriod)
                {
                    task.TemplateCode = "fam_batchPaymentAcceptancePeriodTemplate";
                }
                else if (query.AccountPeriodType == AccountPeriodTypeEnum.WarrantyPeriod)
                {
                    task.TemplateCode = "fam_batchPaymentWarrantyPeriodTemplate";
                }
                else
                {
                    return new BaseResponseData<List<ExportTaskResDto>>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "账期类型错误"
                    };
                }
                var sendUsers = new List<string>
                {
                    (query.UserName ??= string.Empty)
                };
                task.sendUsers = sendUsers;
                task.BatchNo = Guid.NewGuid().ToString();
                task.FileName = string.Concat("账期清单列表导出", DateTime.Now.ToString("yyyyMMddHHmmss"));
                task.IgnoreColumns = string.Empty;
                tasks.Add(task);
                var jsonStr = JsonConvert.SerializeObject(tasks);
                var result = await _coordinateclient.AddTasksAsync(tasks);
                return result;
            }
            catch (Exception ex)
            {
                return new BaseResponseData<List<ExportTaskResDto>>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        private Dictionary<string, object> DtoToDictionary<T>(T input) where T : class
        {
            var dict = new Dictionary<string, object>();
            var properties = typeof(T).GetProperties(); // 获取所有属性

            foreach (var prop in properties)
            {
                if (prop.CanRead && prop.GetValue(input) != null)
                {
                    // 将属性名称和值添加到字典中
                    dict.Add(prop.Name, prop.GetValue(input));
                }
            }
            return dict;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> SetAgentPayClassifyAsync(List<AgentBankInput> input)
        {
            if (input == null || input.Count <= 0)
            {
                return BaseResponseData<int>.Failed(500, "操作失败，原因：请选择供应商");
            }

            var paymentAutoItemId = input.First().PaymentAutoItemId;
            var agentIds = input.Select(p => p.AgentId).Distinct().ToList();
            var paymentAutoAgentBanks = await _paymentAutoAgentBankInfoQueryService.GetAllListAsync(p => p.PaymentAutoItemId == paymentAutoItemId && p.AgentId.HasValue && agentIds.Contains(p.AgentId.Value));
            if (paymentAutoAgentBanks != null && paymentAutoAgentBanks.Count > 0)
            {
                var exitAgentIds = paymentAutoAgentBanks.Select(p => p.AgentId.Value).ToList();
                foreach (var item in paymentAutoAgentBanks)
                {
                    item.PayClassify = input.First().PayClassify;
                }
                _db.PaymentAutoAgentBankInfos.UpdateRange(paymentAutoAgentBanks);

                var newAgentBanks = input.Where(p => !exitAgentIds.Contains(p.AgentId)).ToList();
                if (newAgentBanks != null && newAgentBanks.Count > 0)
                {
                    List<PaymentAutoAgentBankInfoPo> list = new List<PaymentAutoAgentBankInfoPo>();
                    foreach (var item in newAgentBanks)
                    {
                        var paymentAutoAgentBank = new PaymentAutoAgentBankInfoPo { };
                        paymentAutoAgentBank.Id = Guid.NewGuid();
                        paymentAutoAgentBank.PaymentAutoItemId = item.PaymentAutoItemId;
                        paymentAutoAgentBank.AgentId = item.AgentId;
                        paymentAutoAgentBank.AgentName = item.AgentName;
                        paymentAutoAgentBank.Account = item.Account;
                        paymentAutoAgentBank.AccountName = item.Account;
                        paymentAutoAgentBank.BankCode = item.BankCode;
                        paymentAutoAgentBank.BankName = item.BankName;
                        paymentAutoAgentBank.Contractno = item.Contractno;
                        paymentAutoAgentBank.CostBearingParty = item.CostBearingParty;
                        paymentAutoAgentBank.importGoods = item.importGoods;
                        paymentAutoAgentBank.InvoiceNo = item.InvoiceNo;
                        paymentAutoAgentBank.PayClassify = item.PayClassify;
                        paymentAutoAgentBank.Paymentabroad = item.Type.HasValue && item.Type.Value == 2 ? 1 : 0;
                        paymentAutoAgentBank.Postscript = item.Postscript;
                        paymentAutoAgentBank.Transactioncoding = item.Transactioncoding;
                        paymentAutoAgentBank.TransferDiscourse = item.TransferDiscourse;
                        paymentAutoAgentBank.Ynpush = item.Ynpush;
                        list.Add(paymentAutoAgentBank);
                    }
                    await _db.PaymentAutoAgentBankInfos.AddRangeAsync(list);
                }
            }
            else
            {
                List<PaymentAutoAgentBankInfoPo> list = new List<PaymentAutoAgentBankInfoPo>();
                foreach (var item in input)
                {
                    var paymentAutoAgentBank = new PaymentAutoAgentBankInfoPo { };
                    paymentAutoAgentBank.Id = Guid.NewGuid();
                    paymentAutoAgentBank.PaymentAutoItemId = item.PaymentAutoItemId;
                    paymentAutoAgentBank.AgentId = item.AgentId;
                    paymentAutoAgentBank.AgentName = item.AgentName;
                    paymentAutoAgentBank.Account = item.Account;
                    paymentAutoAgentBank.AccountName = item.Account;
                    paymentAutoAgentBank.BankCode = item.BankCode;
                    paymentAutoAgentBank.BankName = item.BankName;
                    paymentAutoAgentBank.Contractno = item.Contractno;
                    paymentAutoAgentBank.CostBearingParty = item.CostBearingParty;
                    paymentAutoAgentBank.importGoods = item.importGoods;
                    paymentAutoAgentBank.InvoiceNo = item.InvoiceNo;
                    paymentAutoAgentBank.PayClassify = item.PayClassify;
                    paymentAutoAgentBank.Paymentabroad = item.Type.HasValue && item.Type.Value == 2 ? 1 : 0;
                    paymentAutoAgentBank.Postscript = item.Postscript;
                    paymentAutoAgentBank.Transactioncoding = item.Transactioncoding;
                    paymentAutoAgentBank.TransferDiscourse = item.TransferDiscourse;
                    paymentAutoAgentBank.Ynpush = item.Ynpush;
                    list.Add(paymentAutoAgentBank);
                }
                await _db.PaymentAutoAgentBankInfos.AddRangeAsync(list);
            }
            await _db.SaveChangesAsync();
            return BaseResponseData<int>.Success("操作成功！");
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> SetAgentTransferDiscourseAsync(List<AgentBankInput> input)
        {
            if (input == null || input.Count <= 0)
            {
                return BaseResponseData<int>.Failed(500, "操作失败，原因：请选择供应商");
            }

            var paymentAutoItemId = input.First().PaymentAutoItemId;
            var agentIds = input.Select(p => p.AgentId).Distinct().ToList();
            var paymentAutoAgentBanks = await _paymentAutoAgentBankInfoQueryService.GetAllListAsync(p => p.PaymentAutoItemId == paymentAutoItemId && p.AgentId.HasValue && agentIds.Contains(p.AgentId.Value));
            if (paymentAutoAgentBanks != null && paymentAutoAgentBanks.Count > 0)
            {
                var exitAgentIds = paymentAutoAgentBanks.Select(p => p.AgentId.Value).ToList();
                foreach (var item in paymentAutoAgentBanks)
                {
                    item.TransferDiscourse = input.First().TransferDiscourse;
                }
                _db.PaymentAutoAgentBankInfos.UpdateRange(paymentAutoAgentBanks);

                var newAgentBanks = input.Where(p => !exitAgentIds.Contains(p.AgentId)).ToList();
                if (newAgentBanks != null && newAgentBanks.Count > 0)
                {
                    List<PaymentAutoAgentBankInfoPo> list = new List<PaymentAutoAgentBankInfoPo>();
                    foreach (var item in newAgentBanks)
                    {
                        var paymentAutoAgentBank = new PaymentAutoAgentBankInfoPo { };
                        paymentAutoAgentBank.Id = Guid.NewGuid();
                        paymentAutoAgentBank.PaymentAutoItemId = item.PaymentAutoItemId;
                        paymentAutoAgentBank.AgentId = item.AgentId;
                        paymentAutoAgentBank.AgentName = item.AgentName;
                        paymentAutoAgentBank.Account = item.Account;
                        paymentAutoAgentBank.AccountName = item.Account;
                        paymentAutoAgentBank.BankCode = item.BankCode;
                        paymentAutoAgentBank.BankName = item.BankName;
                        paymentAutoAgentBank.Contractno = item.Contractno;
                        paymentAutoAgentBank.CostBearingParty = item.CostBearingParty;
                        paymentAutoAgentBank.importGoods = item.importGoods;
                        paymentAutoAgentBank.InvoiceNo = item.InvoiceNo;
                        paymentAutoAgentBank.PayClassify = item.PayClassify;
                        paymentAutoAgentBank.Paymentabroad = item.Type.HasValue && item.Type.Value == 2 ? 1 : 0;
                        paymentAutoAgentBank.Postscript = item.Postscript;
                        paymentAutoAgentBank.Transactioncoding = item.Transactioncoding;
                        paymentAutoAgentBank.TransferDiscourse = item.TransferDiscourse;
                        paymentAutoAgentBank.Ynpush = item.Ynpush;
                        list.Add(paymentAutoAgentBank);
                    }
                    await _db.PaymentAutoAgentBankInfos.AddRangeAsync(list);
                }
            }
            else
            {
                List<PaymentAutoAgentBankInfoPo> list = new List<PaymentAutoAgentBankInfoPo>();
                foreach (var item in input)
                {
                    var paymentAutoAgentBank = new PaymentAutoAgentBankInfoPo { };
                    paymentAutoAgentBank.Id = Guid.NewGuid();
                    paymentAutoAgentBank.PaymentAutoItemId = item.PaymentAutoItemId;
                    paymentAutoAgentBank.AgentId = item.AgentId;
                    paymentAutoAgentBank.AgentName = item.AgentName;
                    paymentAutoAgentBank.Account = item.Account;
                    paymentAutoAgentBank.AccountName = item.Account;
                    paymentAutoAgentBank.BankCode = item.BankCode;
                    paymentAutoAgentBank.BankName = item.BankName;
                    paymentAutoAgentBank.Contractno = item.Contractno;
                    paymentAutoAgentBank.CostBearingParty = item.CostBearingParty;
                    paymentAutoAgentBank.importGoods = item.importGoods;
                    paymentAutoAgentBank.InvoiceNo = item.InvoiceNo;
                    paymentAutoAgentBank.PayClassify = item.PayClassify;
                    paymentAutoAgentBank.Paymentabroad = item.Type.HasValue && item.Type.Value == 2 ? 1 : 0;
                    paymentAutoAgentBank.Postscript = item.Postscript;
                    paymentAutoAgentBank.Transactioncoding = item.Transactioncoding;
                    paymentAutoAgentBank.TransferDiscourse = item.TransferDiscourse;
                    paymentAutoAgentBank.Ynpush = item.Ynpush;
                    list.Add(paymentAutoAgentBank);
                }
                await _db.PaymentAutoAgentBankInfos.AddRangeAsync(list);
            }
            await _db.SaveChangesAsync();
            return BaseResponseData<int>.Success("操作成功！");
        }

        /// <summary>
        /// 设置现金折扣金额
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<bool>> SetDiscountAmountAsync(SetDiscountAmountInput input)
        {
            if (input == null || input.Ids == null || input.Ids.Count <= 0)
            {
                return BaseResponseData<bool>.Failed(500, "操作失败，原因：请选择付款信息");
            }

            if (input.LimitedDiscount < 0)
            {
                return BaseResponseData<bool>.Failed(500, "操作失败，原因：现金折扣金额不能小于0");
            }

            var paymentAutoItemPo = await _db.PaymentAutoItems.FirstOrDefaultAsync(p => p.Id == input.PaymentItemId);
            if (paymentAutoItemPo == null)
            {
                return BaseResponseData<bool>.Failed(500, "操作失败，原因：批量付款信息不存在");
            }

            if (paymentAutoItemPo.Status != PaymentAutoItemStatusEnum.WaitSubmit)
            {
                return BaseResponseData<bool>.Failed(500, "操作失败，原因：请选择待提交的批量付款单设置现金折扣金额");
            }

            var paymentAutoDetails = await _db.PaymentAutoDetails.Where(p => input.Ids.Contains(p.Id) && p.PaymentAutoItemId == input.PaymentItemId).ToListAsync();
            if (paymentAutoDetails == null || paymentAutoDetails.Count <= 0)
            {
                return BaseResponseData<bool>.Failed(500, "操作失败，原因：不存在的付款信息");
            }

            foreach (var item in paymentAutoDetails)
            {
                item.LimitedDiscount = input.LimitedDiscount;
            }

            await _db.SaveChangesAsync();

            return BaseResponseData<bool>.Success("操作成功！");
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<bool>> AttachFileIds(AddCashDiscountFileInput input)
        {
            var ret = BaseResponseData<bool>.Success("操作成功！");

            try
            {
                var agentBankInfoPo = await _db.PaymentAutoAgentBankInfos.FirstOrDefaultAsync(p => p.Id == input.Id);
                if (agentBankInfoPo != null)
                {
                    var paymentAutoItemPo = await _db.PaymentAutoItems.FirstOrDefaultAsync(p => p.Id == agentBankInfoPo.PaymentAutoItemId);
                    if (paymentAutoItemPo == null)
                    {
                        return BaseResponseData<bool>.Failed(500, "操作失败，原因：批量付款信息不存在");
                    }

                    if (paymentAutoItemPo.Status != PaymentAutoItemStatusEnum.WaitSubmit)
                    {
                        return BaseResponseData<bool>.Failed(500, "操作失败，原因：请选择待提交的批量付款单上传附件");
                    }
                    agentBankInfoPo.AttachFileIds = string.IsNullOrEmpty(agentBankInfoPo.AttachFileIds) ? input.AttachFileIds : agentBankInfoPo.AttachFileIds + "," + input.AttachFileIds;
                    _db.PaymentAutoAgentBankInfos.Update(agentBankInfoPo);
                    await _unitOfWork.CommitAsync();
                }
            }
            catch (Exception ex)
            {
                ret = BaseResponseData<bool>.Failed(500, "操作失败！" + ex.Message);
            }
            return ret;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<PaymentAutoAgentBankInfo> GetById(Guid id)
        {
            var ret = await _db.PaymentAutoAgentBankInfos.FirstOrDefaultAsync(p => p.Id == id);
            return ret.Adapt<PaymentAutoAgentBankInfo>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<bool>> DeleteAgentAttachFileIds(AddCashDiscountFileInput input)
        {
            var ret = BaseResponseData<bool>.Success("操作成功！");
            var agentBankInfoPo = await _db.PaymentAutoAgentBankInfos.AsNoTracking().FirstOrDefaultAsync(c => c.Id == input.Id);
            if (agentBankInfoPo == null)
            {
                return BaseResponseData<bool>.Failed(500, "操作失败，原因：记录不存在");
            }

            var paymentAutoItemPo = await _db.PaymentAutoItems.FirstOrDefaultAsync(p => p.Id == agentBankInfoPo.PaymentAutoItemId);
            if (paymentAutoItemPo == null)
            {
                return BaseResponseData<bool>.Failed(500, "操作失败，原因：批量付款信息不存在");
            }

            if (paymentAutoItemPo.Status != PaymentAutoItemStatusEnum.WaitSubmit)
            {
                return BaseResponseData<bool>.Failed(500, "操作失败，原因：请选择待提交的批量付款单删除附件");
            }

            var newAttachFileIds = "";
            if (!string.IsNullOrEmpty(agentBankInfoPo.AttachFileIds))
            {
                foreach (var fildId in agentBankInfoPo.AttachFileIds.Split(","))
                {
                    if (!string.IsNullOrEmpty(fildId))
                    {
                        if (fildId.ToLower() != input.AttachFileId.ToLower())
                        {
                            newAttachFileIds += fildId + ",";
                        }
                    }
                }
            }
            newAttachFileIds = newAttachFileIds.TrimEnd(',');
            agentBankInfoPo.AttachFileIds = newAttachFileIds;
            _db.PaymentAutoAgentBankInfos.Update(agentBankInfoPo);
            await _unitOfWork.CommitAsync();
            return ret;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<bool>> SetBulkDiscountAmountAsync(List<PaymentDetailOutput> input)
        {
            if (input == null || input.Count == 0)
            {
                return BaseResponseData<bool>.Failed(500, "操作失败，原因：参数不能为空");
            }
            if (input.Any(p => p.LimitedDiscount < 0))
            {
                return BaseResponseData<bool>.Failed(500, "操作失败，原因：现金折扣金额不能小于0");
            }

            var ids = input.Select(o => o.Id).ToList();
            var paymentAutoDetails = await _db.PaymentAutoDetails.Where(p => ids.Contains(p.Id)).ToListAsync();
            if (paymentAutoDetails == null || paymentAutoDetails.Count == 0)
            {
                return BaseResponseData<bool>.Failed(500, "操作失败，原因：记录不存在");
            }

            var paymentAutoItemIds = paymentAutoDetails.Select(p => p.PaymentAutoItemId).Distinct().ToList();

            if (paymentAutoItemIds.Count == 0)
            {
                return BaseResponseData<bool>.Failed(500, "操作失败，原因：记录不存在");
            }
            else if (paymentAutoItemIds.Count > 1)
            {
                return BaseResponseData<bool>.Failed(500, "操作失败，原因：请选择同一个付款单信息修改");
            }

            var paymentAutoItemPo = await _db.PaymentAutoItems.FirstOrDefaultAsync(p => p.Id == paymentAutoItemIds.FirstOrDefault());
            if (paymentAutoItemPo == null)
            {
                return BaseResponseData<bool>.Failed(500, "操作失败，原因：批量付款信息不存在");
            }

            if (paymentAutoItemPo.Status != PaymentAutoItemStatusEnum.WaitSubmit)
            {
                return BaseResponseData<bool>.Failed(500, "操作失败，原因：请选择待提交的批量付款单设置现金折扣金额");
            }

            foreach (var item in paymentAutoDetails)
            {
                var updateItem = input.FirstOrDefault(t => t.Id == item.Id);
                if (updateItem != null)
                {
                    item.LimitedDiscount = updateItem.LimitedDiscount;
                }
            }

            await _db.SaveChangesAsync();

            return BaseResponseData<bool>.Success("操作成功！");
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<bool>> WithdrawAsync(Guid id, string userName)
        {
            var paymentautoItemPo = await _db.PaymentAutoItems.FirstOrDefaultAsync(p => p.Id == id);
            if (paymentautoItemPo == null)
            {
                return BaseResponseData<bool>.Failed(500, "操作失败，原因：记录不存在");
            }
            else if (paymentautoItemPo.Status != PaymentAutoItemStatusEnum.Auditing)
            {
                return BaseResponseData<bool>.Failed(500, "操作失败，原因：请选择审批中的批量付款单");
            }

            if (!Int32.TryParse(paymentautoItemPo.OARequestId, out var oaRequestId))
            {
                return BaseResponseData<bool>.Failed(500, "操作失败，原因：OA记录ID不存在");
            }

            var weaverOutput = await _weaverApiClient.ForceDrawBackWorkFlow(userName, oaRequestId);
            _logger.LogInformation($"批量付款单撤回，OA记录ID：{oaRequestId}，OA撤回结果：{weaverOutput.ToJson()}");
            if (weaverOutput.Status)
            {
                paymentautoItemPo.Status = PaymentAutoItemStatusEnum.WaitSubmit;
                await _db.SaveChangesAsync();

                return BaseResponseData<bool>.Success("操作成功！");
            }
            else
            {
                return BaseResponseData<bool>.Failed(500, "OA撤回失败，原因：" + weaverOutput.Msg);
            }
        }
    }
}
