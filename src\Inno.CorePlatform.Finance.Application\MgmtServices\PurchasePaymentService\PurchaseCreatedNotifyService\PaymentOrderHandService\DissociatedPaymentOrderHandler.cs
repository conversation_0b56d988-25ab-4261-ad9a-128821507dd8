﻿using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Payments;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.Extensions.DependencyInjection;
using NPOI.POIFS.Properties;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.PurchasePaymentService.PurchaseCreatedNotifyService.PaymentOrderHandService
{
    /// <summary>
    /// 游离付款单处理
    /// </summary>
    public class DissociatedPaymentOrderHandler : PaymentOrderHandler
    {
        public DissociatedPaymentOrderHandler(decimal totalPreAmount, string code, IServiceProvider serviceProvider, PurchaseOutPut purchaseOutPut, AdvancePayOutput currentAdvancePay)
            : base(totalPreAmount, code, serviceProvider, purchaseOutPut, currentAdvancePay)
        {
        }

        public override async Task<decimal> HandPaymentOrder()
        {
            decimal remainintAmount = TotalPreAmount - HandedAmount;
            //处理游离付款单
            var _paymentQueryService = _serviceProvider.GetService<IBaseAllQueryService<PaymentPo>>();
            var _abatementQueryService = _serviceProvider.GetService<IBaseAllQueryService<AbatementPo>>();
            var listPaymentCodes = (await _paymentQueryService.GetAllListAsync(r => r.Code.StartsWith(Code) && string.IsNullOrWhiteSpace(r.PurchaseCode) && r.AbatedStatus == AbatedStatusEnum.NonAbate)).Adapt<List<Payment>>();
            if (listPaymentCodes != null)
            {
                if (listPaymentCodes.Count>1)
                {
                    throw new ApplicationException($"匹配到多个游离付款单，请检查游离付款单数据是否有误，游离付款单号：{Code}");
                }
                foreach (var item in listPaymentCodes)
                {
                    decimal totalAbatementAmount = 0;
                    var abatementItems = await _abatementQueryService.GetAllListAsync(r => r.DebtBillCode == item.Code && r.DebtType == "payment");
                    if (abatementItems != null && abatementItems.Count > 0)
                    {
                        totalAbatementAmount = abatementItems.Sum(r => r.Value);
                    }
                    item.PurchaseCode = CurrentPurchaseOrder.Code;
                    item.ProducerOrderNo = CurrentPurchaseOrder.ProducerOrderNo;
                    item.ProjectId = Guid.Parse(CurrentPurchaseOrder.Project.Id);
                    item.ProjectName = CurrentPurchaseOrder.Project.Name;
                    item.ProjectCode = CurrentPurchaseOrder.Project.Code;
                    item.RelateId = CurrentAdvancePay?.Id;
                    item.PurchaseContactNo = CurrentPurchaseOrder?.Contract?.Code;
                    item.ServiceId = CurrentPurchaseOrder?.Service?.Id;
                    item.ServiceName= CurrentPurchaseOrder?.Service?.Name;
                    item.AdvancePayMode = CurrentAdvancePay?.AdvancePayMode;
                    item.BusinessDeptFullPath = CurrentPurchaseOrder?.businessDeptFullPath;
                    item.BusinessDeptFullName = CurrentPurchaseOrder?.businessDeptFullName;
                    item.BusinessDeptId= CurrentPurchaseOrder?.businessDeptId.ToString();
                    item.CustomerId = CurrentPurchaseOrder?.Hospital?.Id;
                    item.CustomerName= CurrentPurchaseOrder?.Hospital?.Name;
                    item.CoinCode = CurrentPurchaseOrder?.TradeType == null || CurrentPurchaseOrder?.TradeType == TradeTypeEnums.Internal ? "CNY" : CurrentPurchaseOrder.ExternalTradeInfo.CoinAttribute;
                    item.CoinName = CurrentPurchaseOrder?.TradeType == null || CurrentPurchaseOrder?.TradeType == TradeTypeEnums.Internal ? "人民币" : CurrentPurchaseOrder.ExternalTradeInfo.CoinName;

                    decimal currentKdAdAmount = remainintAmount;
                    if ((item.Value - totalAbatementAmount) >= remainintAmount)
                    {//如果游离付款单的金额大于等于当前剩余预付申请要处理的金额
                        HandedAmount += remainintAmount;
                        //拆出来的新单据金额=游离付款单原始金额-已冲销金额-本次处理金额
                        var paymentAmount = item.Value - totalAbatementAmount - remainintAmount;
                        //当前游离付款单的金额=已冲销金额+本次处理金额
                        item.Value = totalAbatementAmount + remainintAmount;
                        //新增一条新的付款单据
                        if (paymentAmount > 0)
                        {
                            //拆单
                            string newCode = item.Code + "-001";
                            await AddPayment(newCode, CurrentAdvancePay, CurrentPurchaseOrder, paymentAmount, CurrentPurchaseOrder.CreatedBy, true, item.PaymentDate, item.OriginCode, null);
                        }
                    }
                    else
                    {//如果游离付款单的金额小于当前剩余预付申请要处理的金额
                        HandedAmount += (item.Value - totalAbatementAmount);
                        currentKdAdAmount = (item.Value - totalAbatementAmount);
                    }
                    AddPaymentAdjustmentInput(currentKdAdAmount, item, "", CurrentPurchaseOrder.businessDeptId.HasValue ? CurrentPurchaseOrder.businessDeptId.ToString() : item.BusinessDeptId, CurrentPurchaseOrder.Code);
                    await InitRMBAmount(CurrentPurchaseOrder, item);
                    await _paymentRepository.UpdateAsync(item);
                }
            }
            if (NextHandler != null && TotalPreAmount - HandedAmount > 0)
            {
                NextHandler.TotalPreAmount = TotalPreAmount;
                NextHandler.HandedAmount = HandedAmount;
                if (ListPushKdPaymentUseInfos != null && NextHandler.ListPushKdPaymentUseInfos != null)
                {
                    NextHandler.ListPushKdPaymentUseInfos.AddRange(ListPushKdPaymentUseInfos);
                }
                return await NextHandler.HandPaymentOrder();
            }
            else
            {
                return TotalPreAmount - HandedAmount;
            }
        }
    }
}
