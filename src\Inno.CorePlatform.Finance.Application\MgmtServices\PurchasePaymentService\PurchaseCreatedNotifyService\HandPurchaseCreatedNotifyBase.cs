﻿using EasyCaching.Core;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Payments;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.AppService;
using Microsoft.Extensions.Logging;
using Dapr.Client;
using Inno.CorePlatform.Common.DDD;
using RestSharp;
using System.Net.Http;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Common.Utility.Strings;
using NetTopologySuite.Index.HPRtree;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Data.Models;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Inno.CorePlatform.Finance.Domain.ValueObjects;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.PurchasePaymentService.PurchaseCreatedNotifyService
{
    public abstract class HandPurchaseCreatedNotifyBase : IHandPurchaseCreatedNotify
    {
        protected PurchaseOutPut CurrentPurchaseOrder { get; set; }
        protected EventBusDTO CurrentContent { get; set; }
        protected List<string> PaymentCodes { get; set; }
        protected IServiceProvider _serviceProvider { get; set; }
        protected IEasyCachingProvider _easyCaching { get; set; }
        protected FinanceDbContext _dbContext { get; set; }

        protected ILogger<HandPurchaseCreatedNotifyBase> _logger { get; set; }
        protected DaprClient _daprClient { get; set; }

        private IUnitOfWork _unitOfWork;

        public async Task Hand(EventBusDTO input, IServiceProvider serviceProvider)
        {
            try
            {
                CurrentContent = input;
                _serviceProvider = serviceProvider;
                _easyCaching = _serviceProvider.GetService<IEasyCachingProvider>();
                _dbContext = _serviceProvider.GetService<FinanceDbContext>();
                _logger = _serviceProvider.GetService<ILogger<HandPurchaseCreatedNotifyBase>>();
                _daprClient = _serviceProvider.GetService<DaprClient>();
                _unitOfWork = _serviceProvider.GetService<IUnitOfWork>();
                await CheckPaymentCodesIsHanding();
                await CreateSubLog(SubLogSourceEnum.Purchase, CurrentContent.ToJson(), "admin", "订阅消息");
                var isHanded = await CheckPrePayIsHanded();
                if (!isHanded)
                {
                    await UpdatePaymentPlan();
                    await PaymentOrderHand();
                }
                var retCommit = await _unitOfWork.CommitAsync();
            }
            catch (Exception ex)
            {

                throw new ApplicationException($"处理失败，原因：{ex.Message}");
            }
            finally
            {
                if (PaymentCodes != null && PaymentCodes.Count > 0)
                {
                    await _easyCaching.RemoveAllAsync(PaymentCodes);
                }
            }
        }
        /// <summary>
        /// 检测预付申请是否已经处理过
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task<bool> CheckPrePayIsHanded()
        {
            return await Task.FromResult(false);
        }
        /// <summary>
        /// 校验付款单号正在被处理中,只有预付且选了付款单号的情况下需要具体处理
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task CheckPaymentCodesIsHanding()
        {
            await Task.CompletedTask;
        }
        /// <summary>
        /// 更新付款计划
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task UpdatePaymentPlan()
        {
            IPurchasePayPlanRepository _purchasePayPlanRepository = _serviceProvider.GetService<IPurchasePayPlanRepository>();
            IPurchaseApiClient _purchaseApiClient = _serviceProvider.GetService<IPurchaseApiClient>();
            CurrentPurchaseOrder = await _purchaseApiClient.GetByIdAsync(CurrentContent.BusinessId.Value);
            if (CurrentPurchaseOrder == null || CurrentPurchaseOrder.PurchaseOrderDetails == null || !CurrentPurchaseOrder.PurchaseOrderDetails.Any())
            {
                throw new ApplicationException($"操作失败：采购单：{CurrentContent.BusinessId}，没有查到采购单");
            }
            if (CurrentPurchaseOrder.PaymentPlans == null || !CurrentPurchaseOrder.PaymentPlans.Any())
            {
                throw new ApplicationException($"操作失败：采购单：{CurrentPurchaseOrder.Code}，没有付款计划");
            }
            string createdBy = (CurrentPurchaseOrder?.CreatedBy) ?? "none";
            await _purchasePayPlanRepository.DeleteByPurchaseNo(CurrentPurchaseOrder.Code);
            foreach (var plan in CurrentPurchaseOrder.PaymentPlans)
            {
                if (plan.APRevisionDetails == null || !plan.APRevisionDetails.Any())
                {
                    throw new Exception($"操作失败：采购单：{CurrentPurchaseOrder.Code}，没有应付修订明细");
                }
                foreach (var detail in plan.APRevisionDetails)
                {
                    var temp = new PurchasePayPlan
                    {
                        Id = Guid.NewGuid(),
                        CompanyId = CurrentPurchaseOrder.Consignee?.Id,
                        CompanyName = (CurrentPurchaseOrder.Consignee?.Name) ?? string.Empty,
                        NameCode = (CurrentPurchaseOrder.Consignee?.NameCode) ?? string.Empty,
                        ServiceId = CurrentPurchaseOrder.Service?.Id,
                        ServiceName = CurrentPurchaseOrder.Service?.Name,
                        AgentId = CurrentPurchaseOrder.Agent?.Id,
                        AgentName = CurrentPurchaseOrder.Agent?.Name,
                        ProductNo = (detail.Product?.ProductNo) ?? string.Empty,
                        ProductId = detail.Product?.Id,
                        PurchaseId = CurrentContent.BusinessId,
                        PurchaseCode = CurrentPurchaseOrder.Code,
                        ForwardPurchaseCode = CurrentPurchaseOrder.ForwardOrder?.Code,
                        AccountPeriodType = (AccountPeriodTypeEnum)plan.DPOType,
                        Ratio = plan.ProportionRevision == null ? 0 : plan.ProportionRevision.Value,
                        Quantity = detail.Quantity,
                        RatioPrice = detail.PurchaseCostRevision,
                        Price = detail.PurchaseCost,
                        ProbablyPayTime = plan.ProbablyPayTime.HasValue ? plan.ProbablyPayTime.Value.DateTime : plan.APTime,
                        CreatedTime = DateTime.Now,
                        UpdatedTime = DateTime.Now,
                        CreatedBy = createdBy,
                        PurchaseDetailId = detail.PurchaseDetailId,
                        UpdatedBy = createdBy,
                        AccountPeriodDays = plan.DPO
                    };
                    await _purchasePayPlanRepository.AddAsync(temp);
                }
            }
        }
        /// <summary>
        /// 付款单处理
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task PaymentOrderHand()
        {
            if (CurrentContent.NegativeDiffAmount.HasValue && CurrentContent.NegativeDiffAmount.Value < 0 && !string.IsNullOrEmpty(CurrentContent.PuacOrderCode))
            {
                decimal leftAmount = Math.Abs(Math.Round(CurrentContent.NegativeDiffAmount.Value, 2));
                List<SavePaymentAdjustmentInput> savePaymentAdjustments = new List<SavePaymentAdjustmentInput>();

                var puacCodes = CurrentContent.PuacOrderCode.Split("-");
                if (puacCodes.Length < 2)
                {
                    throw new ApplicationException($"操作失败：版本修订单号：{CurrentContent.PuacOrderCode}数据异常");
                }
                var _dbContext = _serviceProvider.GetService<FinanceDbContext>();
                // 开启事务
                using var transaction = await _dbContext.Database.BeginTransactionAsync();
                try
                {
                    var payments = await _dbContext.Payments.Where(r => r.PurchaseCode == CurrentPurchaseOrder.Code && r.ProducerOrderNo == CurrentPurchaseOrder.ProducerOrderNo).OrderByDescending(o => o.CreatedTime).ToListAsync();
                    if (payments != null)
                    {
                        var paymentCodes = payments.Select(o => o.Code).ToHashSet();
                        var abatements = await _dbContext.Abatements.Where(r => paymentCodes.Contains(r.DebtBillCode) || paymentCodes.Contains(r.CreditBillCode)).ToListAsync();
                        foreach (var payment in payments)
                        {
                            var abateAmount = abatements?.Where(r => r.DebtBillCode == payment.Code || r.CreditBillCode == payment.Code)?.Sum(o => o.Value) ?? 0;
                            var amount = payment.Value - abateAmount;

                            if (amount > leftAmount && leftAmount > 0 && amount > 0)
                            {
                                var percentage = leftAmount / payment.Value;
                                var value = payment.Value - leftAmount;
                                var originRMBAmount = payment.RMBAmount;

                                payment.Value = value;
                                if (payment.CoinCode == "CNY")
                                {
                                    payment.RMBAmount = value;
                                }
                                else if (payment.RMBAmount.HasValue && payment.CoinCode != "CNY")
                                {
                                    payment.RMBAmount = Math.Round(payment.RMBAmount.Value * (1 - percentage), 2);
                                }
                                _dbContext.Payments.Update(payment);

                                //拆游离付款单
                                string newCode = RevisionCode(puacCodes, payment.Code);
                                var newPayment = AddPayment(newCode, leftAmount, originRMBAmount - payment.RMBAmount, payment);
                                await _dbContext.Payments.AddAsync(newPayment);

                                if (string.IsNullOrWhiteSpace(payment.OriginCode))
                                {
                                    throw new ApplicationException($"游离付款单的原始单号不能为空，游离付款单：{payment.Code}");
                                }

                                savePaymentAdjustments.Add(CreatePaymentAdjustment(leftAmount, payment));
                                leftAmount = 0;
                            }
                            else if (amount <= leftAmount && leftAmount > 0 && amount > 0)
                            {
                                if (abateAmount == 0)
                                {
                                    string newCode = RevisionCode(puacCodes, payment.Code);
                                    if (string.IsNullOrWhiteSpace(payment.OriginCode))
                                    {
                                        throw new ApplicationException($"游离付款单的原始单号不能为空，游离付款单：{payment.Code}");
                                    }
                                    payment.Code = newCode;
                                    payment.PurchaseCode = null;
                                    payment.PurchaseContactNo = null;
                                    payment.RelateId = null;
                                    payment.AdvancePayMode = null;
                                    payment.BillDate = DateTime.Now;
                                    payment.UpdatedTime = DateTimeOffset.UtcNow;
                                    _dbContext.Payments.Update(payment);

                                    savePaymentAdjustments.Add(CreatePaymentAdjustment(payment.Value, payment));
                                    leftAmount = leftAmount - payment.Value;
                                }
                                else
                                {
                                    var percentage = amount / payment.Value;
                                    var value = payment.Value - amount;
                                    var originRMBAmount = payment.RMBAmount;

                                    payment.Value = value;
                                    if (payment.CoinCode == "CNY")
                                    {
                                        payment.RMBAmount = value;
                                    }
                                    else if (payment.RMBAmount.HasValue && payment.CoinCode != "CNY")
                                    {
                                        payment.RMBAmount = Math.Round(payment.RMBAmount.Value * (1 - percentage), 2);
                                    }
                                    if (string.IsNullOrWhiteSpace(payment.OriginCode))
                                    {
                                        throw new ApplicationException($"游离付款单的原始单号不能为空，游离付款单：{payment.Code}");
                                    }
                                    _dbContext.Payments.Update(payment);

                                    //拆游离付款单
                                    string newCode = RevisionCode(puacCodes, payment.Code);
                                    var newPayment = AddPayment(newCode, amount, originRMBAmount - payment.RMBAmount, payment);
                                    await _dbContext.Payments.AddAsync(newPayment);

                                    savePaymentAdjustments.Add(CreatePaymentAdjustment(amount, payment));
                                    leftAmount = leftAmount - amount;
                                }
                            }
                        }
                    }
                    if (leftAmount > 0)
                    {
                        var useDebts = await _dbContext.DebtPaymentUseDetails.Where(p =>
                                 p.UseCode == CurrentPurchaseOrder.Code)
                                 .AsNoTracking().ToListAsync();
                        if (useDebts != null && useDebts.Any())
                        {
                            var debtPaymentUses = new List<DebtPaymentUseDetailPo>();
                            var posDebts = useDebts.Where(p => p.UseAmount > 0);
                            var negDebts = useDebts.Where(p => p.UseAmount < 0);
                            var useAmount = posDebts.Sum(p => p.UseAmount);
                            var abatement = negDebts.Sum(p => Math.Abs(p.UseAmount));
                            if (useAmount - abatement > 0)
                            {
                                //if (useAmount - abatement < leftAmount)
                                //{
                                //    leftAmount = useAmount - abatement;
                                //}

                                foreach (var item in posDebts)
                                {
                                    var _amounts = negDebts.Where(w => w.DebtCode == item.DebtCode)?.Sum(s => s.UseAmount) ?? 0;
                                    if (item.UseAmount - _amounts > leftAmount && leftAmount > 0)
                                    {
                                        //记录一个负数使用明细
                                        debtPaymentUses.Add(new DebtPaymentUseDetailPo
                                        {
                                            CreatedBy = "none",
                                            Id = Guid.NewGuid(),
                                            UseCode = item.UseCode,
                                            UseAmount = -leftAmount,
                                            DebtCode = item.DebtCode,
                                            DebtId = item.DebtId,
                                        });
                                        leftAmount = 0;
                                    }
                                    else if (item.UseAmount - _amounts == leftAmount && leftAmount > 0)
                                    {
                                        //记录一个负数使用明细
                                        debtPaymentUses.Add(new DebtPaymentUseDetailPo
                                        {
                                            CreatedBy = "none",
                                            Id = Guid.NewGuid(),
                                            UseCode = item.UseCode,
                                            UseAmount = -leftAmount,
                                            DebtCode = item.DebtCode,
                                            DebtId = item.DebtId,
                                        });
                                        leftAmount = 0;
                                    }
                                    else if (item.UseAmount - _amounts < leftAmount && leftAmount > 0)
                                    {
                                        //记录一个负数使用明细
                                        debtPaymentUses.Add(new DebtPaymentUseDetailPo
                                        {
                                            CreatedBy = "none",
                                            Id = Guid.NewGuid(),
                                            UseCode = item.UseCode,
                                            UseAmount = -(item.UseAmount - _amounts),
                                            DebtCode = item.DebtCode,
                                            DebtId = item.DebtId,
                                        });
                                        leftAmount = leftAmount - (item.UseAmount - _amounts);
                                    }
                                }

                                if (debtPaymentUses.Count > 0)
                                {
                                    await _dbContext.DebtPaymentUseDetails.AddRangeAsync(debtPaymentUses);
                                }
                            }
                        }
                    }

                    if (leftAmount != 0)
                    {
                        var _value = Math.Abs(Math.Round(CurrentContent.NegativeDiffAmount.Value, 2));
                        throw new ApplicationException($"游离付款单金额不足，释放失败。采购请求释放金额：{_value}，财务可释放金额{_value - leftAmount}");
                    }

                    if (savePaymentAdjustments.Count > 0)
                    {
                        _logger.LogInformation($"推送游离付款单金额调整给金蝶：{savePaymentAdjustments.ToJson()}");
                        var _kingdeeApiClient = _serviceProvider.GetService<IKingdeeApiClient>();
                        var kind = await _kingdeeApiClient.SavePaymentAdjustment(savePaymentAdjustments);
                        if (kind != null && kind.Code != CodeStatusEnum.Success)
                        {
                            throw new ApplicationException(kind.Message);
                        }
                    }

                    await _dbContext.SaveChangesAsync();

                    // 提交事务
                    await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    // 回滚事务
                    await transaction.RollbackAsync();
                    _logger.LogInformation($"版本修订调整游离付款单异常：{ex.Message}");
                    throw;
                }
            }

        }

        private SavePaymentAdjustmentInput CreatePaymentAdjustment(decimal adamount, PaymentPo? payment)
        {
            return new SavePaymentAdjustmentInput
            {
                jfzx_adamount = adamount,
                jfzx_orderno = CurrentPurchaseOrder.Code,
                jfzx_adorderno = "",
                jfzx_org = CurrentPurchaseOrder.businessDeptId.HasValue ? CurrentPurchaseOrder.businessDeptId.ToString() : payment.BusinessDeptId,
                jfzx_paymentno = payment.OriginCode,
                jfzx_projectno = payment.ProjectCode
            };
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="puacCodes"></param>
        /// <param name="code"></param>
        /// <returns></returns>
        private static string RevisionCode(string[] puacCodes, string code)
        {
            return code + "-" + puacCodes[puacCodes.Length - 2] + "-" + puacCodes[puacCodes.Length - 1];
        }

        protected async Task<Payment> AddPayment(AdvancePayOutput advance, PurchaseOutPut? purchaseOrder, decimal paymentValue, string createdBy, bool isNoMaster, DateTime? PaymentDate, string? originCode = null, string? oaRequestId = null)
        {
            var payment = new Payment
            {
                Id = Guid.NewGuid(),
                BillDate = DateTime.Now,
                AbatedStatus = AbatedStatusEnum.NonAbate,
                AgentId = purchaseOrder.Agent?.Id,
                AgentName = purchaseOrder.Agent?.Name,
                CompanyId = purchaseOrder.Consignee?.Id,
                CompanyName = (purchaseOrder.Consignee?.Name) ?? string.Empty,
                NameCode = (purchaseOrder.Consignee?.NameCode) ?? string.Empty,
                CreatedBy = createdBy ?? "none",
                UpdatedBy = createdBy ?? "none",
                CreatedTime = DateTimeOffset.UtcNow,
                ServiceId = purchaseOrder.Service?.Id,
                ServiceName = purchaseOrder.Service?.Name,
                PurchaseCode = isNoMaster ? "" : purchaseOrder.Code,
                PaymentDate = isNoMaster ? PaymentDate : null,
                Code = isNoMaster ? purchaseOrder.Code : "",
                ProducerOrderNo = isNoMaster ? "" : purchaseOrder.ProducerOrderNo,
                Value = paymentValue,
                Type = PaymentTypeEnum.Prepay,
                RelateId = advance.Id,
                AdvancePayMode = advance.AdvancePayMode,
                CreditAmount = 0,
                BusinessDeptFullPath = purchaseOrder.businessDeptFullPath,
                BusinessDeptFullName = purchaseOrder.businessDeptFullName,
                BusinessDeptId = purchaseOrder.businessDeptId.ToString(),
                CoinCode = purchaseOrder?.TradeType == null || purchaseOrder?.TradeType == TradeTypeEnums.Internal ? "CNY" : purchaseOrder.ExternalTradeInfo.CoinAttribute,
                CoinName = purchaseOrder?.TradeType == null || purchaseOrder?.TradeType == TradeTypeEnums.Internal ? "人民币" : purchaseOrder.ExternalTradeInfo.CoinName,
                ProjectId = purchaseOrder?.Project == null ? Guid.Empty : Guid.Parse(purchaseOrder.Project.Id),
                ProjectCode = purchaseOrder?.Project == null ? string.Empty : purchaseOrder.Project.Code,
                ProjectName = purchaseOrder?.Project == null ? string.Empty : purchaseOrder.Project.Name,
                PurchaseContactNo = purchaseOrder?.Contract?.Code,
                CustomerId = purchaseOrder?.Hospital?.Id,
                CustomerName = purchaseOrder?.Hospital?.Name,
                OriginCode = originCode,
                OARequestId = oaRequestId,
            };
            await InitRMBAmount(purchaseOrder, payment);
            if (payment.Value > 0)
            {
                var _paymentRepository = _serviceProvider.GetService<IPaymentRepository>();
                await _paymentRepository.AddAsync(payment);
            }
            return payment;
        }

        protected PaymentPo AddPayment(string newCode, decimal paymentValue, decimal? rmbValue, PaymentPo payment)
        {
            var _payment = new PaymentPo
            {
                Id = Guid.NewGuid(),
                BillDate = DateTime.Now,
                AbatedStatus = AbatedStatusEnum.NonAbate,
                AgentId = payment.AgentId,
                AgentName = payment.AgentName,
                CompanyId = payment.CompanyId,
                CompanyName = payment.CompanyName,
                NameCode = payment.NameCode,
                CreatedBy = payment.CreatedBy,
                UpdatedBy = payment.UpdatedBy,
                CreatedTime = DateTimeOffset.UtcNow,
                ServiceId = payment.ServiceId,
                ServiceName = payment.ServiceName,
                PurchaseCode = "",
                PaymentDate = payment.PaymentDate,
                Code = newCode,
                ProducerOrderNo = "",
                Value = paymentValue,
                RMBAmount = rmbValue,
                Type = PaymentTypeEnum.Prepay,
                RelateId = null,
                AdvancePayMode = null,
                CreditAmount = 0,
                BusinessDeptFullPath = payment.BusinessDeptFullPath,
                BusinessDeptFullName = payment.BusinessDeptFullName,
                BusinessDeptId = payment.BusinessDeptId,
                CoinCode = payment.CoinCode,
                CoinName = payment.CoinName,
                ProjectId = payment.ProjectId,
                ProjectCode = payment.ProjectCode,
                ProjectName = payment.ProjectName,
                PurchaseContactNo = "",
                CustomerId = payment.CustomerId,
                CustomerName = payment.CustomerName,
                OriginCode = payment.OriginCode,
            };

            return _payment;
        }

        private async Task InitRMBAmount(PurchaseOutPut? purchaseOrder, Payment payment)
        {
            if (purchaseOrder?.TradeType != null && purchaseOrder?.TradeType == TradeTypeEnums.External)
            {
                if (purchaseOrder.ExternalTradeInfo.CoinName != "人民币")
                {
                    var _exchangeRateService = _serviceProvider.GetService<IExchangeRateService>();
                    var exchange = await _exchangeRateService.GetByExchangeRateWithKD(new DTOs.Payment.GetExchangeRateInput
                    {
                        Effectdate = DateTime.Now,
                        OrgcurName = purchaseOrder.ExternalTradeInfo.CoinName
                    });
                    if (exchange == null || exchange.Code != CodeStatusEnum.Success)
                    {
                        throw new Exception("操作失败：未获取到汇率");
                    }
                    payment.RMBAmount = payment.Value * exchange.Data.Excval;
                }
                else
                {
                    payment.RMBAmount = payment.Value;
                }
            }
            else
            {
                payment.RMBAmount = payment.Value;
            }
        }
        /// <summary>
        /// 推送付款计划给金蝶
        /// </summary>
        /// <param name="payment"></param>
        /// <param name="advancePay"></param>
        /// <returns></returns>
        protected async Task<BaseResponseData<int>> PushPurchasePayPlans(Payment payment, AdvancePayOutput advancePay)
        {

            var ret = BaseResponseData<int>.Failed(500, "操作失败");
            if (payment == null) return ret;
            var inputKD = new KingdeePayApplyDto
            {
                applyPeopleNumber = payment.CreatedBy,
                payOrgNumber = payment.NameCode,
                payType = "202",
                remark = advancePay.FinanceRemark ?? "预付款",
                relateid = payment.RelateId.ToString(),
                applyDetail = new List<KingdeePayApplyDetail> {
                             new KingdeePayApplyDetail
                             {
                                  AgentId=payment.AgentId.Value,
                                  applyDetailData=new List<ApplyDetailData> {
                                       new ApplyDetailData {
                                            gaterAmount=payment.Value,
                                            detailNumber=payment.PurchaseCode??"",
                                            settlementModel=advancePay.SettlementModel??"JSFS04",
                                            orderNumber=payment.PurchaseCode,
                                            projectNumber=payment.ProjectCode,
                                            arrivalNumber=advancePay.CreditInfo?.ArrivalNumber
                                       }
                                  },
                                  bankAccount=advancePay.AgentBank?.bankNo??"",
                                  bankName=advancePay.AgentBank.account??"",
                                  bankBranchName=advancePay.AgentBank.bank??"",
                                  bankBranchNumber=advancePay.AgentBank.bankCode??"",
                                  transferDiscourse=advancePay.TransferDiscourse,
                                  moneyNumber=string.IsNullOrEmpty(payment.CoinCode)?"CNY":payment.CoinCode
                             }
                      },
                businessOrg = payment.BusinessDeptId.ToString(),
                payNumber = string.Empty
            };
            if (advancePay.AgentBank.type == 2)
            {
                inputKD.importGoods = advancePay.CustomsDeclarationGoods;
                inputKD.costBearingParty = advancePay.ChargesBorneBy;
                inputKD.jfzx_ynpush = advancePay.IsBonded.HasValue && advancePay.IsBonded.Value ? "1" : "0";
                inputKD.jfzx_postscript = "";
                inputKD.jfzx_transactioncoding = "121010";
                inputKD.jfzx_paymentabroad = "1";
                inputKD.jfzx_contractno = advancePay.ContractNumber;
                inputKD.jfzx_invoiceno = advancePay.InvoiceNumber;
            }
            var _kingdeeApiClient = _serviceProvider.GetService<IKingdeeApiClient>();
            // TODO: 通过payment.RelateId（采购申请ID）查找对应的OA请求ID，暂时使用业务标识
            var oaRequestId = $"payment_{payment.Id}_{payment.RelateId}";
            inputKD.requestId = oaRequestId;
            ret = await _kingdeeApiClient.PushPaymentApplyToKingdee(inputKD);
            return ret;

        }
        private async Task CreateSubLog(SubLogSourceEnum source, string content, string userName, string operate, bool isCommit = true)
        {
            var _subLogService = _serviceProvider.GetService<ISubLogService>();
            if (_subLogService != null)
                await _subLogService.LogAsync(source.ToString(), content, operate);


        }
    }
}
