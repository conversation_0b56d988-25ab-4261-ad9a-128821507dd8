﻿using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs
{
    public class EventBusDTO
    {
        /// <summary>
        /// 业务类型（如：采购、销售、入库、出库）
        /// </summary>
        public string? BusinessType { get; set; }
        /// <summary>
        /// 业务单据id
        /// </summary>
        public Guid? BusinessId { get; set; }
        /// <summary>
        /// 业务单据号
        /// </summary>
        public string? BusinessCode { get; set; }
        /// <summary>
        /// 业务子类型(如经销购货入库、寄售入库)
        /// </summary>
        public string? BusinessSubType { get; set; }

        #region 采购订单
        /// <summary>
        /// 关系Id
        /// </summary>
        public Guid? RelateId { get; set; }

        /// <summary>
        /// 是否使用单据日期
        /// </summary>
        public bool? useBillDate { get; set; }

        #endregion
        /// <summary>
        /// 是否为自动单据（true=是，null/false=不是）
        /// </summary>
        public bool? IsAutoBill { get; set; }
        /// <summary>
        /// 付款单号集合
        /// </summary>
        public string? PaymentCodes { get; set; }

        /// <summary>
        /// 负数金额
        /// </summary>
        public decimal? NegativeDiffAmount { get; set; }
        public ReviseRangeEnum? ReviseRange { get; set; }

        /// <summary>
        /// 版本修订单号
        /// </summary>
        public string? PuacOrderCode { get; set; }

        /// <summary>
        /// OA请求Id
        /// </summary>
        public string? OARequestId { get; set; }
    }

    /// <summary>
    /// 发布方参数
    /// </summary>
    /// <param name="Code"></param>
    public record PurchaseOrder(string Code, decimal Amount, Guid RelateId);


    public class CreateInvoiceCreditSub
    {
        public string InvoiceNo { get; set; }
        public string CreditNo { get; set; }
        public string OrderNo { get; set; }
        public decimal Amount { get; set; }
        /// <summary>
        /// 开票客户
        /// </summary>
        public string? CustomerId { get; set; }
        /// <summary>
        /// 开票客户
        /// </summary>
        public string? CustomerName { get; set; }
        /// <summary>
        /// 开票公司
        /// </summary>
        public Guid? CompanyId { get; set; }
        /// <summary>
        /// 开票公司
        /// </summary>
        public string? CompanyName { get; set; }
        /// <summary>
        /// 明细
        /// </summary>
        public List<CreateInvoiceCreditSubDetail?>? Details { get; set; }
        /// <summary>
        /// 开票人[旺店通]
        /// </summary>
        public string? InvoiceCreatedBy { get;  set; }
        /// <summary>
        /// 开票时间[旺店通]
        /// </summary>
        public DateTime? InvoiceTime { get;  set; }
    }

    public class CreateInvoiceCreditSubDetail
    {
        public string InvoiceNo { get; set; }
        /// <summary>
        /// 规格型号
        /// </summary>
        public string? Specification { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public decimal? Quantity { get; set; }
        /// <summary>
        /// 价格
        /// </summary>
        public decimal? UnitPrice { get; set; }
        /// <summary>
        /// 开票公司
        /// </summary>
        public Guid? CompanyId { get; set; }
        /// <summary>
        /// 货号id
        /// </summary>
        public Guid? ProductId { get; set; }
    }

    public class CancelInvoicePushSale
    {
        public string? InvoiceNo { get; set; }
    }

    public class CreateInvoicePushSale
    {
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
        /// <summary>
        /// 应收类型
        /// </summary>
        public int? CreditType { get; set; }
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditBillCode { get; set; }
        /// <summary>
        /// 红票号
        /// </summary>
        public string? RedInvoiceNo { get; set; }
        /// <summary>
        /// 蓝票号
        /// </summary>
        public string? BlueInvoiceNo { get; set; }

        /// <summary>
        /// 红冲金额
        /// </summary>
        public decimal? RedInvoiceAmount { get; set; }


        /// <summary>
        /// 红冲应收金额
        /// </summary>
        public decimal? RedCreditAmount { get; set; }
    } 
}
